import mongoose from "mongoose";
import { ONBOARDING_STEP, ProfileStatus } from "../constants/index.js";
import User from "../models/User.js";
import VideoUpload from "../models/VideoUpload.js";
import { getPreviewVideoUrl } from "../services/awsSpace.js";
import Feedback from "../models/Feedback.js";

export const earlyLifeData = async (req, res) => {
	const userId = req.user._id;
	const user = await User.findById(userId);

	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}

	const isAfterApproved =
		user.profileStatus === ProfileStatus.Approved ||
		user.profileStatus === ProfileStatus.ReApproved;

	try {
		let earlyLifeData = user.earlyLifeData;

		if (!earlyLifeData) {
			return res
				.status(404)
				.json({ message: "Early life data not found" });
		}

		let tempVideoPath = null;
		if (earlyLifeData.videoId) {
			tempVideoPath = await getPreviewVideoUrl(
				userId,
				earlyLifeData.videoId
			);
		}

		if (isAfterApproved && user.updatedEarlyLifeData) {
			earlyLifeData = user.updatedEarlyLifeData;
		}

		return res.status(200).json({
			birthCity: earlyLifeData.birthCity,
			hometownCity: earlyLifeData.hometownCity,
			universities: earlyLifeData.universities,
			schools: earlyLifeData.schools,
			earlyLifeTags: earlyLifeData.earlyLifeTags,
			earlyLifeSummary: earlyLifeData.earlyLifeSummary,
			videoUrl: tempVideoPath,
		});
	} catch (error) {
		console.error("Error in earlyLifeData:", error);
		return res
			.status(500)
			.json({ error: "Unable to fetch early life data" });
	}
};

export const professionalLifeData = async (req, res) => {
	const userId = req.user._id;
	const user = await User.findById(userId);

	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}

	const isAfterApproved =
		user.profileStatus === ProfileStatus.Approved ||
		user.profileStatus === ProfileStatus.ReApproved;
	try {
		let professionalLifeData = user.professionalLifeData;

		if (!professionalLifeData) {
			return res
				.status(404)
				.json({ message: "Professional life data not found" });
		}

		let tempVideoPath = null;
		if (professionalLifeData.videoId) {
			tempVideoPath = await getPreviewVideoUrl(
				userId,
				professionalLifeData.videoId
			);
		}

		if (isAfterApproved && user.updatedProfessionalLifeData) {
			professionalLifeData = user.updatedProfessionalLifeData;
		}
		return res.status(200).json({
			firstJob: professionalLifeData.firstJob,
			subsequentJobs: professionalLifeData.subsequentJobs,
			professionalLifeTags: professionalLifeData.professionalLifeTags,
			professionalLifeSummary:
				professionalLifeData.professionalLifeSummary,
			videoUrl: tempVideoPath,
		});
	} catch (error) {
		console.error("Error in professionalLifeData:", error);
		return res
			.status(500)
			.json({ error: "Unable to fetch professional life data" });
	}
};

export const currentLifeData = async (req, res) => {
	const userId = req.user._id;
	const user = await User.findById(userId);

	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}

	const isAfterApproved =
		user.profileStatus === ProfileStatus.Approved ||
		user.profileStatus === ProfileStatus.ReApproved;
	try {
		let currentLifeData = user.currentLifeData;
		if (!currentLifeData) {
			return res
				.status(404)
				.json({ message: "Current life data not found" });
		}

		let tempVideoPath = null;
		if (currentLifeData.videoId) {
			tempVideoPath = await getPreviewVideoUrl(
				userId,
				currentLifeData.videoId
			);
		}

		if (isAfterApproved && user.updatedCurrentLifeData) {
			currentLifeData = user.updatedCurrentLifeData;
		}

		return res.status(200).json({
			currentLifeSummary: currentLifeData.currentLifeSummary,
			currentCities: currentLifeData.currentCities,
			currentOrganizations: currentLifeData.currentOrganizations,
			frequentTravelCities: currentLifeData.frequentTravelCities,
			currentLifeTags: currentLifeData.currentLifeTags,
			videoUrl: tempVideoPath,
		});
	} catch (error) {
		console.error("Error in currentLifeData:", error);
		return res
			.status(500)
			.json({ error: "Unable to fetch current life data" });
	}
};

export const getAllLifeData = async (req, res) => {
	const userId = req.user._id;
	const user = await User.findById(userId);

	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}

	const isAfterApproved =
		user.profileStatus === ProfileStatus.Approved ||
		user.profileStatus === ProfileStatus.ReApproved;

	try {
		let earlyLifeData = user.earlyLifeData;
		let professionalLifeData = user.professionalLifeData;
		let currentLifeData = user.currentLifeData;

		let earlyLifeVideoPath = null;
		let professionalLifeVideoPath = null;
		let currentLifeVideoPath = null;

		if (earlyLifeData && earlyLifeData.videoId) {
			earlyLifeVideoPath = await getPreviewVideoUrl(
				userId,
				earlyLifeData?.videoId
			);
		}

		if (professionalLifeData && professionalLifeData.videoId) {
			professionalLifeVideoPath = await getPreviewVideoUrl(
				userId,
				professionalLifeData?.videoId
			);
		}

		if (currentLifeData && currentLifeData.videoId) {
			currentLifeVideoPath = await getPreviewVideoUrl(
				userId,
				currentLifeData?.videoId
			);
		}

		if (isAfterApproved) {
			if (user.updatedEarlyLifeData) {
				earlyLifeData = user.updatedEarlyLifeData;
			}
			if (user.updatedProfessionalLifeData) {
				professionalLifeData = user.updatedProfessionalLifeData;
			}
			if (user.updatedCurrentLifeData) {
				currentLifeData = user.updatedCurrentLifeData;
			}
		}
		const dataToSend = {
			earlyLifeData: earlyLifeData
				? {
						birthCity: earlyLifeData.birthCity,
						hometownCity: earlyLifeData.hometownCity,
						universities: earlyLifeData.universities,
						schools: earlyLifeData.schools,
						earlyLifeTags: earlyLifeData.earlyLifeTags,
						earlyLifeSummary: earlyLifeData.earlyLifeSummary,
						videoUrl: earlyLifeVideoPath,
					}
				: null,
			professionalLifeData: professionalLifeData
				? {
						firstJob: professionalLifeData.firstJob,
						subsequentJobs: professionalLifeData.subsequentJobs,
						professionalLifeTags:
							professionalLifeData.professionalLifeTags,
						professionalLifeSummary:
							professionalLifeData.professionalLifeSummary,
						videoUrl: professionalLifeVideoPath,
					}
				: null,
			currentLifeData: currentLifeData
				? {
						currentLifeSummary: currentLifeData.currentLifeSummary,
						currentCities: currentLifeData.currentCities,
						currentOrganizations:
							currentLifeData.currentOrganizations,
						frequentTravelCities:
							currentLifeData.frequentTravelCities,
						currentLifeTags: currentLifeData.currentLifeTags,
						videoUrl: currentLifeVideoPath,
					}
				: null,
		};
		return res.status(200).json(dataToSend);
	} catch (error) {
		console.error("Error in getLifeData:", error);
		return res.status(500).json({ error: "Unable to fetch life data" });
	}
};

export const updateTranscribeData = async (req, res) => {
	try {
		const userId = req.body.userId ?? req.user._id;
		const { earlyLife, professionalLife, currentLife } = req.body;
		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (user.displayStatus === false) {
			return res.status(404).json({ message: "User not found" });
		}
		const updateFields = {};
		const isOnboarding =
			user.profileStatus === ProfileStatus.Onboarding ||
			user.profileStatus === ProfileStatus.Pending ||
			user.profileStatus === ProfileStatus.ChangesRequested;

		const isAfterApproved =
			user.profileStatus === ProfileStatus.Approved ||
			user.profileStatus === ProfileStatus.ReApproved;

		if (earlyLife) {
			if (isOnboarding) {
				Object.keys(earlyLife).forEach(key => {
					updateFields[`earlyLifeData.${key}`] = earlyLife[key];
				});
			} else if (isAfterApproved) {
				Object.keys(earlyLife).forEach(key => {
					updateFields[`updatedEarlyLifeData.${key}`] =
						earlyLife[key];
				});
			}
			if (user.onboardingStep === ONBOARDING_STEP.EARLY_LIFE_FORM) {
				updateFields.onboardingStep =
					ONBOARDING_STEP.PROFESSIONAL_LIFE_VIDEO;
			}
			await Feedback.updateMany(
				{
					recipient: userId,
					isActive: true,
					"feedbackMessage.earlyLife": { $exists: true },
				},
				{
					$set: { "actionTaken.earlyLife": true },
				}
			);
		}

		if (professionalLife) {
			if (isOnboarding) {
				Object.keys(professionalLife).forEach(key => {
					updateFields[`professionalLifeData.${key}`] =
						professionalLife[key];
				});
			} else if (isAfterApproved) {
				Object.keys(professionalLife).forEach(key => {
					updateFields[`updatedProfessionalLifeData.${key}`] =
						professionalLife[key];

					if (key === "subsequentJobs") {
						updateFields[`updatedProfessionalLifeData.${key}`] = (
							professionalLife[key] ?? []
						).map(subsequentJob => {
							if (!subsequentJob._id) {
								subsequentJob._id =
									new mongoose.Types.ObjectId();
							}
							return subsequentJob;
						});
					}
				});
			}
			if (
				user.onboardingStep === ONBOARDING_STEP.PROFESSIONAL_LIFE_FORM
			) {
				updateFields.onboardingStep =
					ONBOARDING_STEP.CURRENT_LIFE_VIDEO;
			}
			await Feedback.updateMany(
				{
					recipient: userId,
					isActive: true,
					"feedbackMessage.professionalLife": { $exists: true },
				},
				{
					$set: { "actionTaken.professionalLife": true },
				}
			);
		}

		if (currentLife) {
			if (isOnboarding) {
				Object.keys(currentLife).forEach(key => {
					updateFields[`currentLifeData.${key}`] = currentLife[key];
				});
			} else if (isAfterApproved) {
				Object.keys(currentLife).forEach(key => {
					updateFields[`updatedCurrentLifeData.${key}`] =
						currentLife[key];

					if (key === "currentOrganizations") {
						updateFields[`updatedCurrentLifeData.${key}`] = (
							currentLife[key] ?? []
						).map(organization => {
							if (!organization._id) {
								organization._id =
									new mongoose.Types.ObjectId();
							}
							return organization;
						});
					}
				});
			}
			if (user.onboardingStep === ONBOARDING_STEP.CURRENT_LIFE_FORM) {
				updateFields.onboardingStep = ONBOARDING_STEP.FINAL_SUBMIT;
			}
			await Feedback.updateMany(
				{
					recipient: userId,
					isActive: true,
					"feedbackMessage.currentLife": { $exists: true },
				},
				{
					$set: { "actionTaken.currentLife": true },
				}
			);
		}

		if (isAfterApproved) {
			updateFields.profileStatus = ProfileStatus.ReApproved;
		}

		if (userId !== req.user._id) {
			const updatedBy = user.updatedBy ?? [];
			updatedBy.push(req.user._id);
			updateFields.updatedBy = updatedBy;
		}
		const updatedUser = await User.findByIdAndUpdate(
			userId,
			{ $set: updateFields },
			{ new: true }
		);

		if (!updatedUser) {
			return res.status(404).json({ message: "User not found" });
		}

		res.status(200).json({
			message: "Profile updated successfully",
		});
	} catch (error) {
		console.error("Error in updateTranscribeData:", error);
		res.status(500).json({ error: "Server Error" });
	}
};

export const requestFinalReview = async (req, res) => {
	const userId = req.user._id;
	const user = await User.findById(userId);

	try {
		if (
			user &&
			user.earlyLifeData?.videoId &&
			user.professionalLifeData?.videoId &&
			user.currentLifeData?.videoId
		) {
			console.log(
				`check all three videos are processed for user ${userId}`
			);
			const allVideos = await VideoUpload.find({
				_id: {
					$in: [
						user.earlyLifeData.videoId,
						user.professionalLifeData.videoId,
						user.currentLifeData.videoId,
					],
				},
			});
			const allVideosProcessed = allVideos.every(
				video => video.transcriptionStatus === "completed"
			);
			if (!allVideosProcessed) {
				throw new Error(
					"Some videos are not processed yet Please wait"
				);
			}
			user.profileStatus = ProfileStatus.Pending;
			user.onboardingStep = ONBOARDING_STEP.WAIT_FOR_APPROVAL;
			await user.save();
			return res.status(200).json({
				message: "Final review requested successfully",
			});
		}
		return res.status(400).json({
			message: "All videos are not uploaded yet",
		});
	} catch (error) {
		console.error("Error in requestFinalReview:", error);
		res.status(500).json({
			message: error.message ?? "Failed to complete final review",
		});
	}
};
