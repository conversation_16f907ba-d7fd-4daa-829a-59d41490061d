import {
	ActionIcon,
	Card,
	Checkbox,
	Image as MantineImage,
	SimpleGrid,
	type SimpleGridProps,
} from "@mantine/core";
import unavailableImage from "@assets/unavailable-image.png";
import { useHover } from "@mantine/hooks";
import { AnimatePresence, motion } from "framer-motion";
import type { EventDetailsType } from "../../types";
import { useCallback, useState } from "react";
import { GalleryViewer } from "../GalleryViewer";
import { IconStar, IconX } from "@tabler/icons-react";
import { useParams } from "react-router-dom";

type commonProps = {
	isEditMode: boolean;
	removeImage?: (media: File | EventDetailsType["creationMedia"][0]) => void;
	isThumbnail?: string;
	handleThumbnailChange?: (
		e: React.ChangeEvent<HTMLInputElement>,
		img: File | EventDetailsType["creationMedia"][0]
	) => void;
};

type ImageCardsProps = commonProps & {
	gridProps?: SimpleGridProps;
	images: EventDetailsType["creationMedia"];
};

type ImageCardProps = commonProps & {
	media: EventDetailsType["creationMedia"][0];
	imageIndex: number;
	handlePreview: (index: number) => void;
};

const EventImageCards = (props: ImageCardsProps) => {
	const {
		gridProps,
		isEditMode,
		images,
		removeImage,
		isThumbnail,
		handleThumbnailChange,
	} = props;
	const { eventName } = useParams();
	const [viewerOpened, setViewerOpened] = useState<number | null>(null);

	const handlePreview = useCallback((index: number) => {
		setViewerOpened(index);
	}, []);

	return (
		<>
			<SimpleGrid {...gridProps}>
				<AnimatePresence>
					{images.map((media, index) => (
						<ImageCard
							key={media._id || index}
							media={media}
							isEditMode={isEditMode}
							imageIndex={index}
							handlePreview={handlePreview}
							removeImage={removeImage}
							isThumbnail={isThumbnail}
							handleThumbnailChange={handleThumbnailChange}
						/>
					))}
				</AnimatePresence>
			</SimpleGrid>
			{viewerOpened !== null && (
				<GalleryViewer
					opened={viewerOpened !== null}
					onClose={() => setViewerOpened(null)}
					images={images.map(media => media.url)}
					initial={viewerOpened || 0}
					eventName={eventName}
				/>
			)}
		</>
	);
};

const ImageCard = (props: ImageCardProps) => {
	const {
		media,
		isEditMode,
		imageIndex,
		handlePreview,
		removeImage,
		isThumbnail,
		handleThumbnailChange,
	} = props;

	const { hovered, ref } = useHover();

	return (
		<motion.div
			ref={ref}
			layout
			initial={{ opacity: 0, scale: 1 }}
			animate={{
				opacity: 1,
				scale: 1,
				transition: { duration: 0.3, delay: 0.1 * imageIndex },
			}}
			exit={{ opacity: 0, scale: 0.95 }}
		>
			<Card p={"0"} shadow="md" withBorder radius="md">
				<MantineImage
					src={media.url}
					alt={`Event media ${imageIndex + 1}`}
					fallbackSrc={unavailableImage}
					h="100%"
					w="100%"
					styles={{
						root: {
							aspectRatio: "185/100",
						},
					}}
					fit="cover"
					radius={"md"}
					style={{
						filter: hovered
							? "brightness(90%)"
							: "brightness(100%)",
						cursor: "pointer",
					}}
					onClick={() => handlePreview(imageIndex)}
				/>
				{isEditMode && (
					<>
						<ActionIcon
							color="red"
							variant="light"
							radius="xl"
							size="md"
							onClick={() => removeImage?.(media)}
							style={{
								position: "absolute",
								top: 4,
								right: 4,
								opacity: hovered ? 1 : 0,
								transition: "opacity 0.2s",
								pointerEvents: hovered ? "auto" : "none",
							}}
						>
							<IconX size={18} />
						</ActionIcon>
						<Checkbox
							style={{
								position: "absolute",
								backgroundColor: "white",
								top: 4,
								left: 4,
								fontWeight: "600",
								padding: "6px",
								borderRadius: "4px",
								opacity: hovered ? 1 : 0,
							}}
							color="var(--mantine-color-blue-4)"
							styles={{
								label: {
									color: "gray",
									paddingLeft: "4px",
									cursor: "pointer",
								},
								input: {
									borderColor: "var(--mantine-color-blue-4)",
									cursor: "pointer",
								},
							}}
							checked={isThumbnail === media._id}
							onChange={e => handleThumbnailChange?.(e, media)}
							label="Thumbnail"
							size="xs"
						/>
						{isThumbnail === media._id && (
							<ActionIcon
								variant="filled"
								color="yellow"
								radius="xl"
								size="sm"
								style={{
									position: "absolute",
									bottom: 8,
									right: 8,
									zIndex: 2,
								}}
							>
								<IconStar size={16} />
							</ActionIcon>
						)}
					</>
				)}
			</Card>
		</motion.div>
	);
};
export default EventImageCards;
