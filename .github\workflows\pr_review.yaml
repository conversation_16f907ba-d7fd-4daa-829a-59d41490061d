name: PR Agent (<PERSON>)
on:
    pull_request:
        types: [opened, reopened, ready_for_review, 'synchronize']
    issue_comment:
jobs:
    pr_agent_job:
        if: ${{ github.event.sender.type != 'Bot' }}
        runs-on: ubuntu-latest
        permissions:
            issues: write
            pull-requests: write
            contents: write
        steps:
            -   name: PR Agent action step
                uses: qodo-ai/pr-agent@main
                env:
                    GITHUB_TOKEN: ${{ secrets.G_TOKEN }}
                    config.model: "gemini/gemini-2.5-flash-lite"
                    config.fallback_models: '["gemini/gemini-2.5-flash"]'
                    GOOGLE_AI_STUDIO.GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
                    github_action_config.auto_review: "true"
                    github_action_config.auto_describe: "true"
                    github_action_config.auto_improve: "true"
                    github_action_config.debug: "true"
                    github_action_config.pr_actions: '["opened", "reopened", "ready_for_review", "review_requested", "synchronize"]' 
