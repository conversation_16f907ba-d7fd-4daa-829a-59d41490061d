import { <PERSON><PERSON>, <PERSON><PERSON>, Text, Title } from "@mantine/core";
import ProfileTabs from "./ProfileTabs";
import type React from "react";
import apiClient from "../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import { useAuth } from "../contexts/AuthContext";
import { useState } from "react";
import { useNotifications } from "../contexts/NotificationContext";

const ProfileTabsWrapper: React.FC = () => {
	const { fetchUser } = useAuth();
	const { actionInfo } = useNotifications();

	const [loading, setLoading] = useState<boolean>(false);
	const [modalOpened, setModalOpened] = useState(false);

	const proceedWithSubmit = async () => {
		try {
			setLoading(true);
			const response = await apiClient.post(
				"/api/lifeData/requestFinalReview"
			);
			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			fetchUser();
			sessionStorage.removeItem("isStepBusy");
			sessionStorage.removeItem("activeStep");
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message ||
						"Failed to submit for final review.",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to submit for final review.",
					color: "red",
				});
			}
		} finally {
			setLoading(false);
			setModalOpened(false);
		}
	};

	const handleFinalSubmit = () => {
		if (actionInfo.actionCompleted === false) {
			setModalOpened(true);
		} else {
			proceedWithSubmit();
		}
	};

	return (
		<div>
			<Modal
				opened={modalOpened}
				onClose={() => setModalOpened(false)}
				title={<Title order={3}>Confirm Submission</Title>}
				centered
			>
				<div>
					<Text>
						You have not taken action on some feedbacks. Are you
						sure you want to proceed with the submission?
					</Text>
					<div
						style={{
							display: "flex",
							justifyContent: "flex-end",
							marginTop: "20px",
						}}
					>
						<Button
							variant="outline"
							onClick={() => setModalOpened(false)}
							style={{ marginRight: 10 }}
						>
							Cancel
						</Button>
						<Button
							onClick={proceedWithSubmit}
							loading={loading}
							color="red"
						>
							Yes, submit
						</Button>
					</div>
				</div>
			</Modal>
			<div
				style={{
					background: "#fff",
					borderRadius: "12px",
					padding: "16px",
					boxShadow: "0 2px 10px rgba(0,0,0,0.08)",
				}}
			>
				<ProfileTabs stickyTop={0} noEditInProfile={true} />
			</div>

			<Button
				onClick={handleFinalSubmit}
				style={{
					position: "fixed",
					bottom: 50,
					right: 40,
					zIndex: 100,
				}}
				color="blue"
				size="md"
				loading={loading}
			>
				Final Submit
			</Button>
		</div>
	);
};

export default ProfileTabsWrapper;
