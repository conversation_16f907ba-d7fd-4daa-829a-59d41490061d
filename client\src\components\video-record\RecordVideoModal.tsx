import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mantine/core";
import { IconVideo } from "@tabler/icons-react";
import { videoTypeLabel } from "../../constants";
import VideoRecorder from "./VideoRecorder";
import type { videoDataType } from "../../types";

type RecordVideoModalProps = {
	onRecordClick: boolean;
	setOnRecordClick: (value: boolean) => void;
	videoType: videoDataType;
	onVideoSelect: (file: File) => void;
};

const RecordVideoModal = (props: RecordVideoModalProps) => {
	return (
		<>
			<Button
				leftSection={<IconVideo size={20} />}
				onClick={() => props.setOnRecordClick(true)}
				w={"100%"}
				disabled={props.onRecordClick}
				variant="outline"
			>
				Record Video
			</Button>

			<Modal
				opened={props.onRecordClick}
				onClose={() => props.setOnRecordClick(false)}
				title={`Record Video - ${videoTypeLabel[props.videoType as keyof typeof videoTypeLabel]}`}
				radius="md"
				centered
				size="fit-container"
				trapFocus={false}
			>
				<VideoRecorder
					videoType={props.videoType}
					onVideoRecord={blob => {
						const file = new File(
							[blob],
							`recorded-video-${Date.now()}.webm`,
							{
								type: blob.type ?? "video/webm",
							}
						);
						props.onVideoSelect(file);
						props.setOnRecordClick(false);
					}}
				/>
			</Modal>
		</>
	);
};

export default RecordVideoModal;
