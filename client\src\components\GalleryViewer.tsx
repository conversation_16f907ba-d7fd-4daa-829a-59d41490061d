import React from "react";
import unavailableImage from "@assets/unavailable-image.png";

import Lightbox from "yet-another-react-lightbox";
import Download from "yet-another-react-lightbox/plugins/download";
import Counter from "yet-another-react-lightbox/plugins/counter";
import Fullscreen from "yet-another-react-lightbox/plugins/fullscreen";

import "yet-another-react-lightbox/styles.css";
import "yet-another-react-lightbox/plugins/counter.css";
import "yet-another-react-lightbox/plugins/thumbnails.css";

interface GalleryViewerProps {
	opened: boolean;
	onClose: () => void;
	images: string[];
	initial?: number;
	eventName?: string;
}

export const GalleryViewer: React.FC<GalleryViewerProps> = ({
	opened,
	onClose,
	images,
	initial = 0,
	eventName,
}) => {
	const slides = images.map(image => ({
		src: image || unavailableImage,
	}));

	const forceDownload = (url: string) => {
		fetch(url, {
			method: "GET",
			headers: {
				Accept: "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
			},
			cache: "no-cache",
		})
			.then(response => response.blob())
			.then(blob => {
				const link = document.createElement("a");
				link.href = URL.createObjectURL(blob);
				link.download = `${eventName ? `${eventName}-` : ""}${Date.now()}.${blob.type.split("/")[1]}`;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
				URL.revokeObjectURL(link.href);
			})
			.catch(console.error);
	};

	return (
		<Lightbox
			open={opened}
			index={initial}
			close={onClose}
			slides={slides}
			styles={{ container: { backgroundColor: "rgba(0, 0, 0, .8)" } }}
			carousel={{ finite: images.length <= 1 }}
			render={{
				buttonPrev: images.length <= 1 ? () => null : undefined,
				buttonNext: images.length <= 1 ? () => null : undefined,
			}}
			plugins={[Counter, Download, Fullscreen]}
			download={{
				download: ({ slide }) => {
					forceDownload(slide.src);
				},
			}}
		/>
	);
};
