import { Flex, Image, Text } from "@mantine/core";
import empty from "../../assets/empty.svg";

export type EmptyProps = {
	messageComponent?: React.ReactNode;
	messageText?: string;
};

const Empty = ({ messageComponent, messageText }: EmptyProps) => {
	return (
		<Flex
			justify="center"
			align="center"
			direction="column"
			style={{
				position: "absolute",
				top: "50%",
				left: "50%",
				transform: "translateY(-50%)",
			}}
		>
			<Image src={empty} alt="empty" w={140} />
			{messageComponent ? (
				messageComponent
			) : (
				<Text c={"gray"} fw={500}>
					{messageText || "No data found"}
				</Text>
			)}
		</Flex>
	);
};

export default Empty;
