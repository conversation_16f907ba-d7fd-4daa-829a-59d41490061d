name: Run the qodo-cover action

on:
    workflow_dispatch:
        inputs:
            desired_coverage:
                description: "Desired coverage percentage"
                required: false
                default: "70"

permissions:
    pull-requests: write # Allows commenting on pull requests
    contents: write # Allows reading and writing files

jobs:
    run-qodo-cover:
        runs-on: ubuntu-22.04
        steps:
            -   name: Check out repo
                uses: actions/checkout@v3
            -   name: qodo-cover
                uses: qodo-ai/qodo-ci/.github/actions/qodo-cover@v0.1.12
                with:
                    github_token: ${{ secrets.GITHUB_TOKEN }}
                    branch: ${{ github.ref_name }}
                    project_language: python
                    project_root: .
                    code_coverage_report_path: ./coverage.xml
                    coverage_type: cobertura
                    test_command: "pytest --cov=. --cov-report=xml --cov-report=term" # your test command
                    model: gpt-4o-2024-11-20
                    max_iterations: 3
                    desired_coverage: 90
                    run_each_test_separately: true
                    source_folder: .
                    test_folder: .
                    additional_instructions: "generated tests MUST be prefixed by a comment that says 'This test was generated by Qodo Cover'"
                env:
                    OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}