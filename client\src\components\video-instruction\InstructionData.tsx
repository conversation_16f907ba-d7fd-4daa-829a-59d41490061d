import type { videoDataType } from "../../types";

export const InstructionData = (videoType: videoDataType) => {
	switch (videoType) {
		case "EarlyLife":
			return {
				title: "Early Life",
				description:
					"In this video, tell gang members the story of your early life.",
				sections: [
					{
						points: [
							"Talk about places where you were born and grew up.",
							"Various cities you lived in and experienced.",
							"Talk about family and parents.",
							"Who were your friends — what did you do together?",
							"What were the things that interested you?",
							"Which educational institutes did you attend? Describe your experiences in these schools / colleges / universities etc. Give some idea of timelines.",
							"Anything else that feels natural for this video.",
						],
					},
				],
			};

		case "ProfessionalLife":
			return {
				title: "Professional Life",
				description:
					"The intent of this video would be to give a glimpse of your career journey, kind of roles, organisations, learnings, experiences, locations and more.\nYou can speak about the following in this video, and try to follow a chronological order with a sense of timelines:",
				sections: [
					{
						points: [
							"Number of years in your professional journey.",
							"Cover the various jobs (Including titles (analyst, director, etc.) / roles  you have had as an intern, employee, founder, owner, freelancer",
							"Name the organisations you have worked with, what role and work you did, how long, which cities / countries",
							"From each role - share the specific learnings or experiences or incidents that stand out or seem worth sharing",
							"Cover all career except what you are doing right now",
						],
					},
				],
			};

		case "CurrentLife":
			return {
				title: "Current Life",
				description:
					"Give gang members an overview of your current life:",
				sections: [
					{
						title: "Personal",
						points: [
							"Location / base, family, friends, areas of interest",
						],
					},
					{
						title: "Professional",
						points: [
							"Name of your current organization and your work profile ?",
							"How and when did you start this journey / role ?",
							"What work does the organization do - please describe what problems are solved via your products, solutions, services",
							"What is the current state? What progress has been made in terms of products, customers, revenues, team size or any thing else",
							"Anything interesting you want to share about your organisation / startup, team etc.",
						],
					},
				],
			};

		default:
			return {
				title: "Video Instructions",
				description:
					"Please record or upload your video following the relevant guidelines.",
				sections: [],
			};
	}
};
