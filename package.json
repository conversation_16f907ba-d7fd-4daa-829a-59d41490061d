{"name": "360-back", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"precommit:client": "cd client && npm run precommit", "precommit:server": "cd server && npm run precommit", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky", "up:sonar": "docker compose up"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.8.0", "devDependencies": {"husky": "^9.1.7", "prettier": "^3.6.2", "sonarqube-scanner": "^4.3.0"}}