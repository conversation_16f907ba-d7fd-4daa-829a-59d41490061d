import mongoose from "mongoose";

const feedbackSchema = new mongoose.Schema(
	{
		sender: {
			type: mongoose.Schema.Types.ObjectId, // user id who send feedback
			ref: "User",
			required: true,
		},
		recipient: {
			type: mongoose.Schema.Types.ObjectId, // feedback target (profile owner)
			ref: "User",
			required: true,
		},
		feedbackMessage: {
			basicDetails: {
				type: String,
			},
			earlyLife: {
				type: String,
			},
			professionalLife: {
				type: String,
			},
			currentLife: {
				type: String,
			},
		},
		actionTaken: {
			basicDetails: { type: Boolean },
			earlyLife: { type: Boolean },
			professionalLife: { type: Boolean },
			currentLife: { type: Boolean },
		},
		readBy: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }], // is User Read
		isActive: { type: Boolean, default: true }, // once profile is approved this should be false
	},
	{ timestamps: true }
);

const Feedback = mongoose.model("Feedback", feedbackSchema);

export default Feedback;
