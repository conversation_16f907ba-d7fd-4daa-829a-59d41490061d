import { useState } from "react";
import {
	Card,
	Image,
	Text,
	Box,
	Flex,
	Stack,
	Menu,
	ActionIcon,
} from "@mantine/core";
import { Calendar, Clock } from "lucide-react";
import type { EventListType } from "../../../types";
import { useNavigate } from "react-router-dom";
import {
	IconDotsVertical,
	IconEdit,
	IconTrash,
	IconUserShare,
} from "@tabler/icons-react";
import { useHover } from "@mantine/hooks";
import unavailableImage from "@assets/unavailable-image.png";
import openCustomModal from "../../modals/CustomModal";
import { useAuth } from "../../../contexts/AuthContext";
import { roleValues } from "../../../constants";

type EventCardProps = {
	event: EventListType;
	handleDelete?: (eventId: string) => Promise<void>;
	handleEdit?: (eventId: string, eventName: string) => void;
	handlePublish?: (eventId: string) => void;
	isDraft?: boolean;
};

export const EventCard: React.FC<EventCardProps> = ({
	event,
	handleDelete,
	handleEdit,
	handlePublish,
	isDraft,
}) => {
	const navigate = useNavigate();
	const { hovered, ref } = useHover();
	const { user } = useAuth();

	const [menuOpened, setMenuOpened] = useState(false);

	const formatDate = (dateInput: string | Date) => {
		const date =
			typeof dateInput === "string" ? new Date(dateInput) : dateInput;
		return date.toLocaleDateString("en-US", {
			month: "short",
			day: "numeric",
			year: "numeric",
		});
	};

	const formatTime = (dateInput: string | Date) => {
		const date =
			typeof dateInput === "string" ? new Date(dateInput) : dateInput;
		return date.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		});
	};

	const handleCardClick = (event: EventListType) => {
		navigate(`/events/event/${event.name}/${event._id}`);
	};

	const thumbnailImage = event.thumbnailImage?.url || unavailableImage;

	return (
		<Card
			ref={ref}
			shadow="xl"
			p={0}
			radius="md"
			withBorder
			onMouseLeave={() => setMenuOpened(false)}
			style={{
				overflow: "hidden",
				boxShadow: "var(--shadow-card)",
				transition: "transform 300ms ease",
			}}
		>
			<Box>
				<Image
					src={thumbnailImage}
					h={280}
					w="100%"
					alt={event.name}
					fit="cover"
					style={{
						background: "var(--mantine-color-gray-3)",
					}}
				/>
				{user?.role !== roleValues.CommunityMember && (
					<Menu
						opened={menuOpened}
						position="bottom-end"
						shadow="sm"
						styles={{
							dropdown: {
								minWidth: "140px",
								borderRadius: "var(--mantine-radius-md)",
								zIndex: 10,
							},
							item: {
								padding: "0.35rem",
								color: "var(--mantine-color-gray-8)",
							},
						}}
						onOpen={() => setMenuOpened(true)}
						onClose={() => setMenuOpened(false)}
					>
						<Menu.Target>
							<Box
								style={{
									position: "absolute",
									top: 16,
									right: 16,
									visibility:
										hovered || menuOpened
											? "visible"
											: "hidden",
									opacity: hovered || menuOpened ? 1 : 0,
								}}
							>
								<ActionIcon
									variant="gradient"
									gradient={{
										from: "blue",
										to: "cyan",
										deg: 45,
									}}
								>
									<IconDotsVertical size={16} />
								</ActionIcon>
							</Box>
						</Menu.Target>
						<Menu.Dropdown>
							<Menu.Item
								variant="default"
								leftSection={<IconEdit size={14} />}
								onClick={() => {
									handleEdit?.(event._id, event.name);
								}}
							>
								Edit
							</Menu.Item>
							<Menu.Item
								onClick={() => {
									openCustomModal({
										title: "Are you sure you want to delete this event?",
										confirmCallback: () =>
											handleDelete?.(event._id),
									});
								}}
								variant="default"
								c={"var(--mantine-color-red-6)"}
								leftSection={<IconTrash size={14} />}
							>
								Delete
							</Menu.Item>
							{isDraft !== undefined && isDraft && (
								<Menu.Item
									onClick={() => {
										openCustomModal({
											title: "Published Event",
											description:
												"Are you sure you want to publish this event?",
											confirmCallback: () =>
												handlePublish?.(event._id),
										});
									}}
									variant="default"
									c={"var(--mantine-color-green-6)"}
									leftSection={<IconUserShare size={14} />}
								>
									Publish
								</Menu.Item>
							)}
						</Menu.Dropdown>
					</Menu>
				)}
			</Box>

			<Stack
				p="md"
				onClick={() => handleCardClick(event)}
				style={{ cursor: "pointer" }}
			>
				<Stack mb="sm">
					<Text fw={700} size="lg" lineClamp={1}>
						{event.name}
					</Text>
					<Text c="dimmed" size="sm" mt={4} lineClamp={1}>
						{event.description}
					</Text>
				</Stack>

				<Box
					pt="sm"
					style={{
						borderTop: "1px solid rgba(0,0,0,0.1)",
					}}
				>
					<Flex align="center" gap="xs" mb={4}>
						<Calendar size={16} color="#228be6" />
						<Text size="sm" fw={500}>
							{formatDate(event.startDate)} -{" "}
							{formatDate(event.endDate)}
						</Text>
					</Flex>

					<Flex align="center" gap="xs">
						<Clock size={16} color="#228be6" />
						<Text size="sm">
							{formatTime(event.startDate)} -{" "}
							{formatTime(event.endDate)}
						</Text>
					</Flex>
				</Box>
			</Stack>
		</Card>
	);
};
