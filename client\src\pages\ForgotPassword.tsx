import {
	Button,
	Center,
	Paper,
	Stack,
	TextInput,
	Title,
	Text,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { IconCheck, IconX } from "@tabler/icons-react";
import { useState } from "react";
import apiClient from "../config/axios";
import { useNavigate } from "react-router-dom";

const ForgotPassword = () => {
	const [email, setEmail] = useState("");
	const [emailError, setEmailError] = useState("");
	const [loading, setLoading] = useState(false);
	const navigate = useNavigate();

	const validateEmail = (email: string): boolean => {
		if (!email) {
			setEmailError("Email is required");
			return false;
		}
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			setEmailError("Please enter a valid email address");
			return false;
		}
		setEmailError("");
		return true;
	};

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		if (!email) {
			notifications.show({
				title: "Validation Error",
				message: "Email is required",
				color: "red",
				icon: <IconX />,
			});
			return;
		}
		setLoading(true);
		try {
			const response = await apiClient.post("/api/auth/forgot-password", {
				email,
			});
			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			navigate("/login");
		} catch (error: any) {
			notifications.show({
				title: "Error",
				message:
					error.response?.data?.message ||
					"Failed to send reset link",
				color: "red",
				icon: <IconX />,
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<Center mih="100vh" bg="gray.0">
			<Paper withBorder shadow="md" radius="md" p="xl" w={460}>
				<Stack gap="sm" align="center">
					<Title order={2}>Forgot Password</Title>

					<Text>
						Enter your email address below and we'll send you a link
						to reset your password.
					</Text>

					<form onSubmit={handleSubmit}>
						<Stack gap="md" mt="md">
							<TextInput
								label="Email"
								placeholder="<EMAIL>"
								required
								value={email}
								w={400}
								onChange={e => {
									setEmail(e.target.value);
									if (emailError) {
										validateEmail(e.target.value);
									}
								}}
								error={emailError}
								disabled={loading}
							/>

							<Button type="submit">
								{loading ? "Sending..." : "Send Reset Link"}
							</Button>
						</Stack>
					</form>
				</Stack>
			</Paper>
		</Center>
	);
};

export default ForgotPassword;
