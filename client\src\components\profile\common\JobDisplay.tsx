import { Stack, Group, Text, ThemeIcon } from "@mantine/core";
import {
	IconBriefcase,
	IconUserCheck,
	IconBuilding,
} from "@tabler/icons-react";
import React from "react";
import type { DiffChange, Job } from "../../../types";
import { getChangedValue, getDiffStyle } from "../../../utils/deep-diff";
import RenderDiff from "./RenderDiff";

interface JobDisplayProps {
	job: Job;
	diffChanges: DiffChange[] | null;
	diffPathPrefix: (string | number)[];
	isFirstJob: boolean;
}

const JobDisplay: React.FC<JobDisplayProps> = ({
	job,
	diffChanges,
	diffPathPrefix,
	isFirstJob,
}) => {
	const isDeletedJob = diffChanges?.some(change => {
		if (
			change.kind === "D" &&
			JSON.stringify(change.path) === JSON.stringify(diffPathPrefix)
		) {
			return true;
		}

		if (change.kind === "A" && change.item?.kind === "D" && !isFirstJob) {
			const arrayPath = diffPathPrefix.slice(0, -1);
			const jobIndex = diffPathPrefix[diffPathPrefix.length - 1];
			if (
				JSON.stringify(change.path) === JSON.stringify(arrayPath) &&
				change.index !== undefined &&
				change.index === jobIndex
			) {
				return true;
			}
		}

		return false;
	});

	const isNewJob = diffChanges?.some(change => {
		if (
			change.kind === "N" &&
			JSON.stringify(change.path) === JSON.stringify(diffPathPrefix)
		) {
			return true;
		}

		if (change.kind === "A" && change.item?.kind === "N" && !isFirstJob) {
			const arrayPath = diffPathPrefix.slice(0, -1);
			const jobIndex = diffPathPrefix[diffPathPrefix.length - 1];
			if (
				JSON.stringify(change.path) === JSON.stringify(arrayPath) &&
				change.index !== undefined &&
				change.index === jobIndex
			) {
				return true;
			}
		}

		return false;
	});

	const companyNameDiffPath = [...diffPathPrefix, "companyName"];
	const rolesDiffPath = [...diffPathPrefix, "roles"];

	return (
		<Stack gap="xs" style={getDiffStyle(isDeletedJob, isNewJob)}>
			<Group align="center" gap="xs">
				<ThemeIcon variant="light" color="blue">
					{isFirstJob ? (
						<IconBriefcase size={20} />
					) : (
						<IconBuilding size={20} />
					)}
				</ThemeIcon>
				<Text className="font-semibold">Company:</Text>
				{(() => {
					const diff = getChangedValue(
						companyNameDiffPath,
						diffChanges
					);
					return diff ? (
						<RenderDiff elementType="text" {...diff} />
					) : (
						<RenderDiff
							elementType="text"
							oldValue={job.companyName}
							newValue={null}
							type={"unchanged"}
						/>
					);
				})()}
			</Group>

			<Group gap="xs">
				{(job.roles && job.roles.length > 0) ||
				diffChanges?.some(
					change =>
						change.kind === "A" &&
						change.item?.kind === "N" &&
						JSON.stringify(change.path) ===
							JSON.stringify(rolesDiffPath)
				) ? (
					<>
						<Group gap="xs">
							<ThemeIcon variant="light" color="teal">
								<IconUserCheck size={16} />
							</ThemeIcon>
							<Text className="font-semibold">Titles:</Text>

							{(() => {
								if (!diffChanges) {
									return (job.roles || []).map(
										(role, idx) => (
											<RenderDiff
												key={idx}
												elementType="tag"
												oldValue={role}
												newValue={null}
												type="unchanged"
											/>
										)
									);
								}

								const originalRoles = job.roles || [];
								const rolesToDisplay: {
									role: string | null;
									type:
										| "unchanged"
										| "added"
										| "deleted"
										| "edited";
									oldRole: string | null;
								}[] = originalRoles.map(role => ({
									role,
									type: "unchanged",
									oldRole: role,
								}));

								const rolesChanges = diffChanges.filter(
									c =>
										c.path &&
										c.path[0] === rolesDiffPath[0] &&
										c.path[1] === rolesDiffPath[1] &&
										(isFirstJob || c.path[2] === "roles")
								);

								rolesChanges.forEach(change => {
									if (
										change.kind === "E" &&
										change.path &&
										typeof change.path[
											rolesDiffPath.length
										] === "number"
									) {
										const index =
											change.path[rolesDiffPath.length];
										if (
											typeof index === "number" &&
											rolesToDisplay[index] !== undefined
										) {
											rolesToDisplay[index] = {
												role: change.rhs,
												type: "edited",
												oldRole: change.lhs,
											};
										}
									} else if (
										change.kind === "A" &&
										change.item &&
										change.index !== undefined
									) {
										if (change.item.kind === "D") {
											const index = change.index;
											if (
												rolesToDisplay[index] !==
												undefined
											) {
												rolesToDisplay[index] = {
													role: change.item.lhs,
													type: "deleted",
													oldRole: change.item.lhs,
												};
											}
										}
									}
								});

								const additions = rolesChanges.filter(
									c =>
										c.kind === "A" &&
										c.item &&
										c.item.kind === "N"
								);

								additions.forEach(change => {
									if (
										change.kind === "A" &&
										change.item &&
										change.index !== undefined
									) {
										rolesToDisplay.splice(change.index, 0, {
											role: change.item.rhs,
											type: "added",
											oldRole: null,
										});
									}
								});

								return rolesToDisplay.map((item, idx) => (
									<RenderDiff
										key={idx}
										elementType="tag"
										oldValue={item.oldRole}
										newValue={item.role}
										type={item.type}
									/>
								));
							})()}
						</Group>
					</>
				) : (
					<Text size="sm" c="dimmed">
						{isFirstJob
							? "No First Job Roles to display."
							: "No Roles to display."}
					</Text>
				)}
			</Group>
		</Stack>
	);
};

export default JobDisplay;
