import { Flex, Box, Text, Group, Divider } from "@mantine/core";
import React from "react";
import RenderDiff from "./RenderDiff";
import type { DiffChange } from "../../../types";

type LifeTagsProps = {
	tags: string[];
	diffChanges: DiffChange[] | null;
	diffPathPrefix: string;
	icon: React.ReactNode;
	label?: string;
	labelProps?: Partial<React.ComponentProps<typeof Text>>;
	dividerLabelProps?: Partial<React.ComponentProps<typeof Text>>;
};

const LifeTags: React.FC<LifeTagsProps> = ({
	tags,
	diffChanges,
	diffPathPrefix,
	icon,
	label,
	labelProps,
	dividerLabelProps,
}) => {
	return (() => {
		const originalTags = tags || [];
		const tagsToDisplay: {
			tag: string | null;
			type: "unchanged" | "added" | "deleted" | "edited";
			oldTag: string | null;
		}[] = originalTags.map((tag: string | null) => ({
			tag,
			type: "unchanged",
			oldTag: tag,
		}));

		if (diffChanges) {
			const tagDiffs = diffChanges.filter(
				change =>
					(change.kind === "A" &&
						JSON.stringify(change.path) ===
							JSON.stringify([diffPathPrefix])) ||
					(change.kind === "E" &&
						change.path &&
						change.path[0] === diffPathPrefix)
			);

			tagDiffs.forEach(change => {
				if (
					change.kind === "E" &&
					change.path &&
					typeof change.path[1] === "number"
				) {
					const index = change.path[1];
					if (tagsToDisplay[index]) {
						tagsToDisplay[index] = {
							tag: change.rhs,
							type: "edited",
							oldTag: change.lhs,
						};
					}
				} else if (
					change.kind === "A" &&
					change.item &&
					change.index !== undefined
				) {
					if (change.item.kind === "D") {
						if (tagsToDisplay[change.index]) {
							tagsToDisplay[change.index].type = "deleted";
						}
					} else if (change.item.kind === "N") {
						tagsToDisplay.splice(change.index, 0, {
							tag: change.item.rhs,
							type: "added",
							oldTag: null,
						});
					}
				}
			});
		}

		return (
			<>
				{dividerLabelProps && <Divider {...dividerLabelProps} />}
				<Flex align="center" gap="sm">
					<Group gap="xs">
						{icon}
						{label && <Text {...labelProps}>{label}:</Text>}
					</Group>
					<Box style={{ flex: 1 }}>
						{tagsToDisplay.length > 0 ? (
							<Flex
								wrap="wrap"
								gap="xs"
								align={"center"}
								justify={"start"}
							>
								{tagsToDisplay.map((item, idx) => (
									<RenderDiff
										key={idx}
										elementType="tag"
										oldValue={item.oldTag}
										newValue={item.tag}
										type={item.type}
									/>
								))}
							</Flex>
						) : (
							<Text size="sm" c="dimmed">
								No tags to display.
							</Text>
						)}
					</Box>
				</Flex>
			</>
		);
	})();
};

export default LifeTags;
