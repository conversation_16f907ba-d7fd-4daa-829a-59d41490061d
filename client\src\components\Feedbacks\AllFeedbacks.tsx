import { notifications } from "@mantine/notifications";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, IconSearch, IconX } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import apiClient from "../../config/axios";
import { useDebouncedValue, useHover } from "@mantine/hooks";
import {
	Badge,
	Container,
	Divider,
	Flex,
	Group,
	Image,
	Indicator,
	Modal,
	Pagination,
	Paper,
	Select,
	Stack,
	Text,
	TextInput,
	Title,
} from "@mantine/core";
import FullScreenLoader from "../FullScreenLoader";
import { resolveImageUrl } from "../../utils/imageUrl";
import { roleLabels, rolesLabelMap } from "../../constants";
import ViewFeedbacks from "./ViewFeedbacks";
import { AnimatePresence, motion } from "framer-motion";
import type { profileStatusDataType } from "../../types";
import EllipsisCell from "../EllipsisCell";
import empty from "../../assets/empty.svg";

type UserFields = {
	_id: string;
	firstName: string;
	secondName: string;
	email: string;
	image: string;
	role: number;
	profileStatus: profileStatusDataType;
};

type UserCardProps = {
	_id: string;
	user: UserFields;
	feedbackCount: number;
};

export const UserCard = ({
	filterStatus,
	...userData
}: UserCardProps & { filterStatus: string }) => {
	const { hovered, ref } = useHover();
	const [viewFeedbacks, setViewFeedbacks] = useState<boolean>(false);

	return (
		<>
			<Paper
				withBorder
				ref={ref}
				style={{
					backgroundColor: hovered ? "#f9fafb" : "transparent",
					borderRadius: "8px",
					cursor: "pointer",
				}}
				key={userData._id}
				p={"lg"}
				onClick={() => setViewFeedbacks(!viewFeedbacks)}
			>
				<Flex justify="space-between" align="center">
					<Group>
						<Indicator
							zIndex={1}
							disabled={userData.feedbackCount === 0}
							size={20}
							label={
								<AnimatePresence mode="wait">
									<motion.span
										key={userData.feedbackCount}
										initial={{ scale: 0, opacity: 0 }}
										animate={{ scale: 1, opacity: 1 }}
										exit={{ scale: 0, opacity: 0 }}
										transition={{ duration: 0.25 }}
									>
										{userData.feedbackCount > 9
											? "9+"
											: userData.feedbackCount}
									</motion.span>
								</AnimatePresence>
							}
							color="red"
							offset={4}
							position="top-end"
							withBorder
						>
							<Image
								src={resolveImageUrl(userData.user.image)}
								alt={`${userData.user.firstName} ${userData.user.secondName}`}
								w={50}
								h={50}
								radius={"xl"}
								fallbackSrc={`https://api.dicebear.com/5.x/initials/svg?seed=${userData.user.firstName} ${userData.user.secondName}`}
							/>
						</Indicator>
						<Flex direction="column" justify="center">
							<Group gap="xs">
								<Text fw={500} size="sm">
									{userData.user.firstName}{" "}
									{userData.user.secondName}
								</Text>
								<Badge variant="outline" size="xs">
									{
										rolesLabelMap[
											roleLabels[
												userData.user
													.role as keyof typeof roleLabels
											]
										]
									}
								</Badge>
							</Group>

							<Stack gap={2} style={{ flex: 1 }}>
								<Group gap="xs"></Group>

								<Text size="sm" c="dimmed">
									{userData.user.email}
								</Text>
							</Stack>
						</Flex>
					</Group>
				</Flex>
			</Paper>

			{viewFeedbacks && (
				<Modal
					opened={viewFeedbacks}
					onClose={() => setViewFeedbacks(false)}
					size="xl"
					title={
						<EllipsisCell
							value={`${userData.user.firstName} ${userData.user.secondName}'s Feedbacks`}
							maxWidth={480}
						/>
					}
					centered
					trapFocus={false}
				>
					<ViewFeedbacks
						userIdFromModal={userData.user._id}
						filtersFromModal={{ status: filterStatus }}
						profileStatus={userData.user.profileStatus}
					/>
				</Modal>
			)}
		</>
	);
};

const AllFeedbacks = () => {
	const [usersData, setUsersData] = useState<UserCardProps[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [filters, setFilters] = useState<{
		page: number;
		totalPages: number;
		status: string;
		search: string;
	}>({
		page: 1,
		totalPages: 1,
		status: "active",
		search: "",
	});
	const [totalUsers, setTotalUsers] = useState(0);
	const setPageToOne = useRef<boolean>(false);

	const [debouncedSearch] = useDebouncedValue(filters.search, 500);

	const getAllFeedbacks = useCallback(async () => {
		try {
			setLoading(true);
			const response = await apiClient.get(
				`/api/feedbacks/all-feedbacks?page=${filters.page}&status=${filters.status}&search=${debouncedSearch}`
			);
			setPageToOne.current = false;

			setUsersData(response.data.data);
			setFilters({
				page: response.data.page,
				status: filters.status,
				search: debouncedSearch,
				totalPages: response.data.totalPages,
			});
			setTotalUsers(response.data.total);
		} catch (error) {
			console.error(error);
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed",
					message:
						error.response?.data?.message ||
						"Unable to fetch Profile Data",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Unable to fetch Profile Data",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	}, [filters.page, filters.status, debouncedSearch]);

	useEffect(() => {
		if (setPageToOne.current && filters.page !== 1) {
			setFilters(prev => ({ ...prev, page: 1 }));
			return;
		}
		getAllFeedbacks();
	}, [filters.page, getAllFeedbacks]);

	const { start, end } = useMemo(() => {
		const limit = 10;
		const start = (filters.page - 1) * limit + 1;
		const end = Math.min(filters.page * limit, totalUsers);
		return { start, end };
	}, [filters.page, totalUsers]);

	return (
		<>
			<Container>
				<Title order={1}>Feedbacks</Title>

				<Divider my={"md"} />

				<Paper withBorder p="md" mb="md">
					<Flex justify="space-between" mb="lg" gap={20}>
						<TextInput
							placeholder="Search user by name or email"
							leftSection={<IconSearch size={16} />}
							value={filters.search}
							w={"100%"}
							onChange={e => {
								setFilters({
									...filters,
									search: e.target.value,
								});
							}}
						/>

						<Select
							leftSection={<IconFilter size={14} />}
							w={180}
							value={filters.status}
							onChange={value => {
								setFilters({
									...filters,
									status: value ?? "active",
									page: 1,
								});
							}}
							data={[
								{ label: "Active", value: "active" },
								{
									label: "Archived",
									value: "archived",
								},
							]}
							size="sm"
							allowDeselect={false}
						/>
					</Flex>
					<Group align="center" justify="space-between">
						<Text c="gray.7" size="sm">
							Showing {totalUsers > 0 ? start : 0}-{end} of{" "}
							{totalUsers} users
						</Text>
						<Pagination
							value={filters.page}
							onChange={page => {
								setFilters({
									...filters,
									page,
								});
							}}
							total={filters.totalPages}
							siblings={1}
							boundaries={1}
						/>
					</Group>
				</Paper>

				{loading && <FullScreenLoader />}
				<Stack gap={12}>
					{usersData.length > 0 ? (
						usersData.map(userData => (
							<UserCard
								key={userData.user._id}
								{...userData}
								filterStatus={filters.status}
							/>
						))
					) : (
						<Flex
							justify="center"
							align="center"
							direction="column"
						>
							<Image src={empty} alt="empty" w={140} />
							<Text c={"gray"} fw={500}>
								No {filters.status} feedbacks found.
							</Text>
						</Flex>
					)}
				</Stack>
			</Container>
		</>
	);
};

export default AllFeedbacks;
