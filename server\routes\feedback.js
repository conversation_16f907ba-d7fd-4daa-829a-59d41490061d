import express from "express";
import { checkIsAdmin, checkLogin } from "../middleware/auth.js";
import {
	getActionInfo,
	getAllFeedbacks,
	getNotifications,
	getUserFeedbacks,
	markFeedbackAsRead,
	sendFeedback,
} from "../controllers/feedback.controller.js";

const router = express.Router();

router.get("/all-feedbacks", checkLogin, checkIsAdmin, getAllFeedbacks);

router.post("/send-feedback", checkLogin, checkIsAdmin, sendFeedback);

router.get(
	"/get-user-feedbacks/:userId",
	checkLogin,
	checkIsAdmin,
	getUserFeedbacks
);

router.get("/notifications", checkLogin, getNotifications);

router.post("/read/:feedbackId", checkLogin, markFeedbackAsRead);

router.get("/action-info", checkLogin, getActionInfo);
export default router;
