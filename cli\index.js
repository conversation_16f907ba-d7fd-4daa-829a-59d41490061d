import { Command } from 'commander';
import { scan } from "@sonar/scan"
import path from "path";

const getProjectConfig = (projectType) => {
    const baseConfig = {
        frontend: {
            path: path.join(import.meta.dirname, "../client"),
            name: '360-Frontend',
            description: '360 Frontend Application'
        },
        backend: {
            path: '.',
            name: path.join(import.meta.dirname, "../server"),
            description: '360 Backend Application'
        }
    };

    return baseConfig[projectType];
};

function runSonarScanner(serverUrl, token, projectType) {
    const projectConfig = getProjectConfig(projectType);

    if (!projectConfig) {
        console.error('Invalid project type. Use either "frontend" or "backend"');
        process.exit(1);
    }

    scan(
        {
            serverUrl,
            token,
            options: {
                'sonar.projectBaseDir': path.join(import.meta.dirname, '../'),
                'sonar.projectName': projectConfig.name,
                'sonar.projectDescription': projectConfig.description,
                'sonar.sources': `${projectConfig.path}`,
                'sonar.exclusions': `**/node_modules/**`,
            },
        },
        () => process.exit()
    );
}

const program = new Command();

program
    .name('sonar-cli')
    .description('Run SonarQube scan')
    .version('1.0.0');

program
    .command('scan')
    .description('Run SonarQube analysis')
    .requiredOption('-u, --url <serverUrl>', 'SonarQube server URL')
    .requiredOption('-t, --token <token>', 'SonarQube authentication token')
    .requiredOption('-p, --project <type>', 'Project type (frontend/backend)')
    .action((options) => {
        runSonarScanner(options.url, options.token, options.project.toLowerCase());
    });

program.parse(process.argv);
