import { Tooltip } from "@mantine/core";
import { useRef, useState, useEffect } from "react";

interface EllipsisCellProps {
	value: string;
	maxWidth?: number;
}

const EllipsisCell = ({ value, maxWidth = 150 }: EllipsisCellProps) => {
	const ref = useRef<HTMLDivElement>(null);
	const [isTruncated, setIsTruncated] = useState(false);

	useEffect(() => {
		const el = ref.current;
		if (el) {
			setIsTruncated(el.scrollWidth > el.clientWidth);
		}
	}, [value, maxWidth]);

	return (
		<Tooltip
			label={value}
			withArrow
			disabled={!isTruncated}
			style={{
				maxWidth: "1000px",
				whiteSpace: "normal",
				wordBreak: "break-word",
			}}
		>
			<div
				ref={ref}
				style={{
					maxWidth,
					overflow: "hidden",
					textOverflow: "ellipsis",
					whiteSpace: "nowrap",
				}}
			>
				{value}
			</div>
		</Tooltip>
	);
};

export default EllipsisCell;
