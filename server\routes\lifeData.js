import express from "express";
import {
	currentLifeData,
	earlyLifeData,
	professionalLifeData,
	updateTranscribeData,
	requestFinalReview,
	getAllLifeData,
} from "../controllers/lifeData.controller.js";
import { checkLogin } from "../middleware/auth.js";

const router = express.Router();

router.get("/earlyLife", checkLogin, earlyLifeData);
router.get("/professionalLife", checkLogin, professionalLifeData);
router.get("/currentLife", checkLogin, currentLifeData);
router.get("/allLifeData", checkLogin, getAllLifeData);

router.post("/requestFinalReview", checkLogin, requestFinalReview);
router.post("/update", checkLogin, updateTranscribeData);

export default router;
