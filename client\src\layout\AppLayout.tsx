import {
	ActionIcon,
	AppShell,
	Divider,
	Group,
	Image,
	NavLink,
	Stack,
	TextInput,
	Title,
} from "@mantine/core";
import {
	Routes,
	Route,
	useLocation,
	useNavigate,
	matchPath,
	Navigate,
} from "react-router-dom";
import PrivateRoute from "../components/PrivateRoute";
import Login from "../pages/Login";
import { useDisclosure } from "@mantine/hooks";
import {
	IconUser,
	IconView360Number,
	IconUserCheck,
	IconSearch,
	type IconProps,
	type Icon,
	IconX,
	IconMessageDots,
	IconCalendarWeek,
} from "@tabler/icons-react";
import { useAuth } from "../contexts/AuthContext";
import Users from "../pages/Users";
import ForgotPassword from "../pages/ForgotPassword";
import ResetPassword from "../pages/ResetPassword";
import NotFound from "../pages/NotFound";
import ProfileTabs from "../components/ProfileTabs";
import { APP_NAME, ONBOARDING_STEP, roleValues } from "../constants";
import OnBoardingStepper from "../pages/OnBoardingStepper";
import WaitForApproval from "../components/WaitForApproval";
import ProfileReview from "../pages/ProfileReview";
import Header from "../components/Header";
import PendingApprovals from "../pages/PendingApprovals";
import { openUserSearchModal } from "../components/modals/UserSearchModal";
import SearchedUser from "../pages/SearchedUser";
import { useMemo } from "react";
import SwitchingPanel from "../components/SwitchingPanel";
import FullScreenLoader from "../components/FullScreenLoader";
import ViewFeedbacks from "../components/Feedbacks/ViewFeedbacks";
import AllFeedbacks from "../components/Feedbacks/AllFeedbacks";
import Events from "../pages/Events";
import { CreateEventForm, EventDetails } from "../components/events";

type NavItemsDataType =
	| {
			type: "navigation";
			label: string;
			icon: React.ForwardRefExoticComponent<
				IconProps & React.RefAttributes<Icon>
			>;
			path: string;
			showIf: boolean;
	  }
	| {
			type: "divider";
			label: string;
			showIf: boolean;
	  };

function AppLayout() {
	const { user, isUpdatingUser, isSwitchingAdminPanel } = useAuth();
	const location = useLocation();
	const [opened, { toggle }] = useDisclosure(false);
	const navigate = useNavigate();

	const navItems: NavItemsDataType[] = useMemo(() => {
		if (!user) return [];
		const isNotCommunityMember = user.role !== roleValues.CommunityMember;
		const items = [
			{
				type: "navigation" as const,
				label: "Profile",
				icon: IconView360Number,
				path: "/profile",
				showIf: true,
			},
			{
				type: "navigation" as const,
				label: "Events",
				icon: IconCalendarWeek,
				path: "/events",
				showIf: true,
			},
			{
				type: "divider" as const,
				label: "Admin Controls",
				showIf: isNotCommunityMember,
			},
			{
				type: "navigation" as const,
				label: "Users",
				icon: IconUser,
				path: "/users",
				showIf: isNotCommunityMember,
			},
			{
				type: "navigation" as const,
				label: "Pending Profiles",
				icon: IconUserCheck,
				path: "/pending-profiles",
				showIf: isNotCommunityMember,
			},
			{
				type: "navigation" as const,
				label: "Feedbacks",
				icon: IconMessageDots,
				path: "/feedbacks",
				showIf: isNotCommunityMember,
			},
		];
		return items;
	}, [user]);

	const isOnboardingStep = useMemo(() => {
		if (!user) return false;
		if (
			user.onboardingStep < ONBOARDING_STEP.WAIT_FOR_APPROVAL &&
			!user.isAdminPanelUser
		) {
			return true;
		}
		return false;
	}, [user]);

	const isWaitForApproval = useMemo(() => {
		if (!user) return false;
		if (
			user.onboardingStep === ONBOARDING_STEP.WAIT_FOR_APPROVAL &&
			!user.isAdminPanelUser
		) {
			return true;
		}
		return false;
	}, [user]);

	const isPublicRoute =
		location.pathname === "/login" ||
		location.pathname === "/forgot-password" ||
		matchPath("/reset-password/:token", location.pathname);

	const isAdminUser = useMemo(() => {
		if (!user) return false;
		if (
			user.role === roleValues.Admin ||
			user.role === roleValues.SuperAdmin
		) {
			return true;
		}
		return false;
	}, [user]);

	if (isUpdatingUser || isSwitchingAdminPanel) {
		return <FullScreenLoader />;
	}

	if (!user) {
		return (
			<Routes>
				<Route path="/login" element={<Login />} />
				<Route path="/forgot-password" element={<ForgotPassword />} />
				<Route
					path="/reset-password/:token"
					element={<ResetPassword />}
				/>
				<Route path="*" element={<Navigate to="/login" replace />} />
			</Routes>
		);
	}

	if (isPublicRoute) {
		if (user.onboardingStep <= ONBOARDING_STEP.WAIT_FOR_APPROVAL) {
			return <Navigate to="/" replace />;
		}
		const redirectTo =
			user.role === roleValues.CommunityMember ? "/profile" : "/users";
		return <Navigate to={redirectTo} replace />;
	}

	if (isOnboardingStep) {
		return (
			<AppShell
				layout="alt"
				header={{ height: 90 }}
				styles={{
					root: { overflow: "hidden" },
				}}
			>
				<Header />

				<AppShell.Main>
					<Routes>
						<Route
							path="/:step?/:tab?"
							element={<OnBoardingStepper />}
						/>
						<Route path="/switching" element={<SwitchingPanel />} />
						<Route
							path="*"
							element={<Navigate to="/:step?" replace />}
						/>
					</Routes>
				</AppShell.Main>
			</AppShell>
		);
	}

	if (isWaitForApproval) {
		return (
			<AppShell layout="alt" header={{ height: 90 }}>
				<Header />

				<AppShell.Main>
					<Routes>
						<Route path="/" element={<WaitForApproval />} />
						<Route path="/switching" element={<SwitchingPanel />} />
						<Route path="*" element={<Navigate to="/" replace />} />
					</Routes>
				</AppShell.Main>
			</AppShell>
		);
	}

	return (
		<AppShell
			layout="alt"
			padding="md"
			header={{ height: 90 }}
			navbar={{
				width: 300,
				breakpoint: "sm",
				collapsed: { mobile: !opened },
			}}
		>
			<Header opened={opened} toggle={toggle} showTitle={false} />

			<AppShell.Navbar h="100%" p="md">
				<Stack>
					<Group justify="space-between" align="center" mb={12}>
						<Group align="center">
							<Image
								src="/assets/SMLogoTransparent.png"
								alt="SM"
								h={60}
								w={60}
								onClick={() => navigate("/")}
								style={{ cursor: "pointer" }}
								fit="contain"
							/>
							<Title order={1} fw={600}>
								{APP_NAME}
							</Title>
						</Group>
						{opened && (
							<ActionIcon
								hiddenFrom="sm"
								size={"md"}
								variant="transparent"
								c="black"
								onClick={toggle}
							>
								<IconX size={28} />
							</ActionIcon>
						)}
					</Group>

					<TextInput
						leftSection={<IconSearch size={16} />}
						placeholder="Search users..."
						pointer
						readOnly
						onClick={e => {
							e.currentTarget.blur();
							openUserSearchModal();
						}}
					/>

					{navItems.map((item, index) => {
						if (item.type === "divider") {
							return (
								<Divider
									key={index}
									label={item.label}
									hidden={!item.showIf ? true : false}
								/>
							);
						}
						return (
							<NavLink
								key={index}
								label={item.label}
								leftSection={<item.icon size={16} />}
								onClick={() => {
									navigate(item.path);
									toggle();
								}}
								active={location.pathname.startsWith(item.path)}
								hidden={!item.showIf ? true : false}
							/>
						);
					})}
				</Stack>
			</AppShell.Navbar>

			<AppShell.Main>
				<Routes>
					<Route
						path="/"
						element={
							<PrivateRoute>
								{user?.role === roleValues.CommunityMember ? (
									<Navigate to="/profile" replace />
								) : (
									<Navigate to="/users" replace />
								)}
							</PrivateRoute>
						}
					/>
					{user?.role !== roleValues.CommunityMember && (
						<>
							<Route
								path="/users"
								element={
									<PrivateRoute>
										<Users />
									</PrivateRoute>
								}
							/>
							{user?.canApproveUser && (
								<Route path="/pending-profiles">
									<Route
										index
										element={
											<PrivateRoute>
												<PendingApprovals />
											</PrivateRoute>
										}
									/>
									<Route path="user/:userId/:tab">
										<Route
											index
											element={
												<PrivateRoute>
													<ProfileReview />
												</PrivateRoute>
											}
										/>
										<Route
											path="feedbacks"
											element={<ViewFeedbacks />}
										></Route>
									</Route>
								</Route>
							)}
							<Route
								path="/feedbacks"
								element={
									<PrivateRoute>
										<AllFeedbacks />
									</PrivateRoute>
								}
							/>
						</>
					)}
					<Route path="/profile">
						<Route
							index
							element={<Navigate to="basic-details" replace />}
						/>
						<Route
							path=":tab"
							element={
								<PrivateRoute>
									<ProfileTabs />
								</PrivateRoute>
							}
						/>
					</Route>

					{user?.canApproveUser && (
						<Route path="/pending-profiles">
							<Route
								index
								element={
									<PrivateRoute>
										<PendingApprovals />
									</PrivateRoute>
								}
							/>
							<Route
								path="user/:userId/:tab?"
								element={
									<PrivateRoute>
										<ProfileReview />
									</PrivateRoute>
								}
							></Route>
						</Route>
					)}
					<Route path="/events">
						<Route
							index
							element={
								<PrivateRoute>
									<Events />
								</PrivateRoute>
							}
						/>
						<Route
							path="event/:eventName/:eventId"
							element={
								<PrivateRoute>
									<EventDetails />
								</PrivateRoute>
							}
						/>
						{isAdminUser && (
							<>
								<Route
									path="create-event"
									element={
										<PrivateRoute>
											<CreateEventForm />
										</PrivateRoute>
									}
								/>
								<Route
									path="update-event/:eventName/:eventId"
									element={
										<PrivateRoute>
											<CreateEventForm />
										</PrivateRoute>
									}
								/>
							</>
						)}
					</Route>
					<Route path="search/:userId">
						<Route
							index
							element={<Navigate to="basic-details" replace />}
						/>
						<Route
							path=":tab"
							element={
								<PrivateRoute>
									<SearchedUser />
								</PrivateRoute>
							}
						/>
					</Route>
					<Route path="/switching" element={<SwitchingPanel />} />
					<Route path="*" element={<NotFound />} />
				</Routes>
			</AppShell.Main>
		</AppShell>
	);
}

export default AppLayout;
