import { useCallback, useEffect, useRef, useState } from "react";
import { apiClient } from "../config/axios";
import {
	Container,
	Title,
	Divider,
	Space,
	Group,
	Button,
	TextInput,
	Loader,
	Center,
	Pagination,
	Text,
	Paper,
	Stack,
	Select,
	Tooltip,
	ActionIcon,
} from "@mantine/core";
import CreateUserForm from "../components/CreateUserForm";
import UserTable from "../components/UserTable";
import { notifications } from "@mantine/notifications";
import {
	IconX,
	IconUserPlus,
	IconCheck,
	IconSearch,
	IconSortAscending,
	IconSortDescending,
} from "@tabler/icons-react";
import type { UserCreation } from "../types";
import { useAuth } from "../contexts/AuthContext";
import { Modal } from "@mantine/core";
import { useDebouncedValue } from "@mantine/hooks";
import { DEBOUNCE_TIME_IN_MS, sortOptions } from "../constants";

type sortByDataType = "firstName" | "secondName" | "email" | "updatedAt";
const Users = () => {
	const { user } = useAuth();
	const [users, setUsers] = useState<UserCreation[]>([]);
	const [filters, setFilters] = useState<{
		page: number;
		totalPages: number;
		limit: number;
		sortBy: sortByDataType;
		searchQuery: string;
		sortOrder: 1 | -1;
	}>({
		page: 1,
		totalPages: 1,
		limit: 10,
		sortBy: "updatedAt",
		searchQuery: "",
		sortOrder: -1,
	});

	const setPageToOne = useRef<boolean>(false);

	const [loading, setLoading] = useState(true);
	const currentUserRole: number = user?.role ?? 0;
	const [opened, setOpened] = useState(false);
	const [userToEdit, setUserToEdit] = useState<UserCreation | null>(null);
	const [totalUsers, setTotalUsers] = useState(0);

	const start = (filters.page - 1) * filters.limit + 1;
	const end = Math.min(filters.page * filters.limit, totalUsers);

	const [debouncedSearch] = useDebouncedValue(
		filters.searchQuery,
		DEBOUNCE_TIME_IN_MS
	);

	const fetchUsers = useCallback(
		async (currentPage: number) => {
			setLoading(true);
			try {
				const res = await apiClient.get(
					`/api/users?page=${currentPage}&limit=${filters.limit}&sort=${filters.sortBy}&sortOrder=${filters.sortOrder}&search=${encodeURIComponent(debouncedSearch.trim())}`
				);

				setPageToOne.current = false;

				setUsers(res.data.data);
				setFilters(previous => {
					return {
						...previous,
						page: currentPage,
						totalPages: res.data.totalPages,
					};
				});
				setTotalUsers(res.data.total);
			} catch (err) {
				console.error("Error fetching users:", err);
				notifications.show({
					title: "Error",
					message: "Failed to fetch users",
					color: "red",
					icon: <IconX />,
				});
			} finally {
				setLoading(false);
			}
		},
		[debouncedSearch, filters.limit, filters.sortBy, filters.sortOrder]
	);

	useEffect(() => {
		if (setPageToOne.current && filters.page !== 1) {
			setFilters(prev => ({ ...prev, page: 1 }));
			return;
		}
		fetchUsers(filters.page);
	}, [fetchUsers, filters.page]);

	const handleUserCreated = (newUser: UserCreation) => {
		setUsers(prevUsers => [...prevUsers, newUser]);
		fetchUsers(filters.page);
	};

	const handleUserUpdated = (updatedUser: UserCreation) => {
		setUsers(prevUsers =>
			prevUsers.map(user =>
				user._id === updatedUser._id ? updatedUser : user
			)
		);
		setUserToEdit(null);
	};

	const handleEditUser = (user: UserCreation) => {
		setUserToEdit(user);
		setOpened(true);
	};

	const handleDeleteUser = async (userId: string) => {
		try {
			const res = await apiClient.delete(`/api/users/delete/${userId}`);
			setUsers(prev => prev.filter(u => u._id !== userId));
			notifications.show({
				title: "User Deleted",
				message: res.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			fetchUsers(filters.page);
		} catch {
			notifications.show({
				title: "Failed",
				message: "Failed to delete user",
				color: "red",
				icon: <IconX />,
			});
		}
	};

	const getAllowedRoles = (): (
		| "SuperAdmin"
		| "Admin"
		| "CommunityMember"
	)[] => {
		if (currentUserRole === 1) {
			return ["SuperAdmin", "Admin", "CommunityMember"];
		}
		if (currentUserRole === 2) {
			return ["CommunityMember", "Admin"];
		}
		return [];
	};

	return (
		<Container>
			<Group justify="space-between">
				<Title order={1}>Users</Title>
				{getAllowedRoles().length > 0 && (
					<Button
						leftSection={<IconUserPlus size={14} />}
						onClick={() => setOpened(true)}
					>
						Create User
					</Button>
				)}
			</Group>
			<Divider my="md" />

			{getAllowedRoles().length > 0 && (
				<>
					<Modal
						opened={opened}
						onClose={() => {
							setOpened(false);
							setUserToEdit(null);
						}}
						title={userToEdit ? "Edit User" : "Create User"}
						size="lg"
						centered
						trapFocus={false}
					>
						<CreateUserForm
							allowedRoles={getAllowedRoles()}
							onUserCreated={
								userToEdit
									? handleUserUpdated
									: handleUserCreated
							}
							setOpened={setOpened}
							userToEdit={userToEdit}
						/>
						<Space h="md" />
					</Modal>
				</>
			)}

			<Paper withBorder p="md" mb="md">
				<Stack gap={6}>
					<Group justify="space-between" align="center">
						<Group align="center" gap={3}>
							<TextInput
								leftSection={<IconSearch size={16} />}
								placeholder="Search by name or email"
								value={filters.searchQuery}
								// readOnly={loading}
								w={300}
								onChange={e => {
									setPageToOne.current = true;
									setFilters(prev => ({
										...prev,
										searchQuery: e.target.value,
									}));
								}}
								size="sm"
							/>
							<Tooltip
								label={`Select field to sort by (current: ${sortOptions.find(opt => opt.value === filters.sortBy)?.label || filters.sortBy})`}
							>
								<Select
									placeholder="Sort by"
									w={180}
									value={filters.sortBy}
									disabled={loading}
									onChange={value => {
										setFilters(prev => ({
											...prev,
											sortBy: value as sortByDataType,
											page: 1,
										}));
									}}
									data={sortOptions}
									size="sm"
									leftSection={
										<Tooltip
											label={`Toggle sort order: currently ${filters.sortOrder === 1 ? "Ascending (A→Z)" : "Descending (Z→A)"}`}
										>
											<ActionIcon
												variant="light"
												onClick={() => {
													setFilters(prev => ({
														...prev,
														sortOrder:
															prev.sortOrder === 1
																? -1
																: 1,
													}));
												}}
												disabled={loading}
											>
												{filters.sortOrder === 1 ? (
													<IconSortAscending
														size={16}
													/>
												) : (
													<IconSortDescending
														size={16}
													/>
												)}
											</ActionIcon>
										</Tooltip>
									}
								/>
							</Tooltip>
						</Group>

						<Select
							w={180}
							value={String(filters.limit)}
							disabled={loading}
							onChange={value => {
								setFilters(prev => ({
									...prev,
									limit: Number(value),
									page: 1,
								}));
							}}
							data={[
								{ label: "5 per page", value: "5" },
								{ label: "10 per page", value: "10" },
								{ label: "20 per page", value: "20" },
								{ label: "50 per page", value: "50" },
							]}
							size="sm"
							allowDeselect={false}
						/>
					</Group>

					<Group align="center" justify="space-between">
						<Text c="gray.7" size="sm">
							Showing {totalUsers > 0 ? start : 0}-{end} of{" "}
							{totalUsers} users
						</Text>
						<Pagination
							disabled={loading}
							total={filters.totalPages}
							value={filters.page}
							onChange={page => {
								setFilters(prev => ({ ...prev, page }));
							}}
							mt="md"
							siblings={1}
							boundaries={1}
						/>
					</Group>
				</Stack>
			</Paper>

			<div>
				{loading ? (
					<Center>
						<Loader />
					</Center>
				) : (
					<>
						<UserTable
							users={users}
							currentUserRole={currentUserRole}
							onDelete={handleDeleteUser}
							onEdit={handleEditUser}
						/>
						<Group justify="flex-end">
							<Pagination
								total={filters.totalPages}
								value={filters.page}
								onChange={page => {
									setFilters(prev => ({ ...prev, page }));
								}}
								mt="md"
								siblings={1}
								boundaries={1}
								disabled={loading}
							/>
						</Group>
					</>
				)}
			</div>
		</Container>
	);
};

export default Users;
