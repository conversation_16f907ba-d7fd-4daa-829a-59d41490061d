import { useHover } from "@mantine/hooks";
import type { FeedbackDataType } from "../../types";
import { useState } from "react";
import apiClient from "../../config/axios";
import {
	Accordion,
	Badge,
	Divider,
	Group,
	Spoiler,
	Stack,
	Text,
	Tooltip,
} from "@mantine/core";
import { User } from "lucide-react";
import { IconMailOpened } from "@tabler/icons-react";

const formatTimestamp = (date: Date) => {
	const now = new Date();
	const diffInMs = now.getTime() - date.getTime();

	const diffInSeconds = Math.floor(diffInMs / 1000);
	if (diffInSeconds < 60) return "just now";

	const diffInMinutes = Math.floor(diffInSeconds / 60);
	if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

	const diffInHours = Math.floor(diffInMinutes / 60);
	if (diffInHours < 24) return `${diffInHours}h ago`;

	const diffInDays = Math.floor(diffInHours / 24);
	if (diffInDays < 30) return `${diffInDays}d ago`;

	const diffInMonths = Math.floor(diffInDays / 30);
	if (diffInMonths < 12) return `${diffInMonths}mo ago`;

	const diffInYears = Math.floor(diffInMonths / 12);
	return `${diffInYears}y ago`;
};

const categoryConfig: Record<
	string,
	{ label: string; color: string; borderColor: string }
> = {
	basicDetails: {
		label: "Basic Details",
		color: "var(--mantine-color-blue-5)",
		borderColor: "var(--mantine-color-blue-2)",
	},
	earlyLife: {
		label: "Early Life",
		color: "var(--mantine-color-green-5)",
		borderColor: "var(--mantine-color-green-2)",
	},
	professionalLife: {
		label: "Professional Life",
		color: "var(--mantine-color-violet-5)",
		borderColor: "var(--mantine-color-violet-2)",
	},
	currentLife: {
		label: "Current Life",
		color: "var(--mantine-color-orange-5)",
		borderColor: "var(--mantine-color-orange-2)",
	},
};

const getCategoryBadge = (category: string) => {
	const config = categoryConfig[category];
	if (!config) return null;

	return (
		<Badge
			key={category}
			style={{
				border: "1px solid",
				borderColor: config.borderColor,
				textTransform: "capitalize",
				padding: "12px",
			}}
			variant="light"
			color={config.color}
			size="md"
		>
			{config.label}
		</Badge>
	);
};

export const FeedbackCard = ({
	feedback,
	setCounts,
	isFromNotification,
}: {
	feedback: FeedbackDataType;
	setCounts?: () => void;
	isFromNotification?: boolean;
}) => {
	const { hovered, ref } = useHover();
	const [isRead, setIsRead] = useState<boolean>(feedback.isReadByCurrentUser);
	const [collapsedOpen, setCollapsedOpen] = useState(false);

	const handleControlClick = async () => {
		if (!isRead) {
			try {
				await apiClient.post(`/api/feedbacks/read/${feedback._id}`);
				setIsRead(true);
				feedback.isReadByCurrentUser = true;
				setCounts?.();
			} catch (error) {
				console.error("Failed to mark feedback as read", error);
			}
		}
		setCollapsedOpen(prev => !prev);
	};

	return (
		<Accordion.Item
			value={feedback._id}
			ref={ref}
			style={{
				backgroundColor: hovered
					? "#f9fafb"
					: !isRead && isFromNotification
						? "#f2fafb"
						: "transparent",
				border:
					!isRead && isFromNotification
						? "1px solid var(--mantine-color-blue-2)"
						: "1px solid var(--mantine-color-gray-3)",
				borderRadius: "8px",
			}}
		>
			<Accordion.Control onClick={handleControlClick}>
				<Stack gap="xs">
					<Group gap={5} align="center" c={"black"}>
						<User className="w-4 h-4 text-muted-foreground" />
						<Text size="md" fw={600}>
							{feedback.sender.firstName}{" "}
							{feedback.sender.secondName}
						</Text>
						{isFromNotification && !isRead && (
							<Badge
								color="var(--mantine-color-red-5)"
								size="sm"
								ml={12}
								style={{ textTransform: "capitalize" }}
							>
								Unread
							</Badge>
						)}

						{feedback.isReadByRecipient && (
							<Tooltip label="This feedback has been viewed by the recipient.">
								<IconMailOpened
									size={16}
									color="var(--mantine-color-green-6)"
								/>
							</Tooltip>
						)}
					</Group>

					{!collapsedOpen && (
						<Group>
							{Object.keys(feedback.feedbackMessage)
								.slice(0, 2)
								.map(getCategoryBadge)}

							{Object.keys(feedback.feedbackMessage).length >
								2 && (
								<Badge
									variant="outline"
									color="gray"
									size="sm"
									style={{ textTransform: "capitalize" }}
								>
									+
									{Object.keys(feedback.feedbackMessage)
										.length - 2}{" "}
									more
								</Badge>
							)}
						</Group>
					)}

					<Text size="xs" c="dimmed">
						{formatTimestamp(new Date(feedback.createdAt))} •{" "}
						{feedback.sender.email}
					</Text>
				</Stack>
			</Accordion.Control>
			{collapsedOpen && <Divider />}
			<Accordion.Panel p={10}>
				<Stack gap="md">
					{Object.entries(feedback.feedbackMessage).map(
						([category, message]) => {
							return (
								<Stack key={category} gap={4}>
									{getCategoryBadge(category)}
									<Spoiler
										mt={8}
										maxHeight={120}
										showLabel="Show more"
										hideLabel="Show less"
										styles={{
											control: {
												fontSize: "14px",
											},
										}}
										pl={16}
										style={{
											borderLeft: "2px solid #eee",
										}}
									>
										<Text size="sm" c="gray.6">
											{message}
										</Text>
									</Spoiler>
								</Stack>
							);
						}
					)}
				</Stack>
			</Accordion.Panel>
		</Accordion.Item>
	);
};
