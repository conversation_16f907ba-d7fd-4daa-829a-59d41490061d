import express from "express";
import {
	checkIsAdmin,
	checkLogin,
	checkOnboardingStatus,
} from "../middleware/auth.js";
import { upload } from "../middleware/upload.js";
import {
	changeAdminPanelView,
	changeProfileStatus,
	createUser,
	deleteUser,
	getAllUsers,
	getPendingApprovals,
	getProfileForReview,
	getRefererCuratorUser,
	getSearchRefererCurator,
	getSearchUser,
	getUserProfile,
	updateProfile,
	updateSpecificProfileById,
	updateUserFromAdmin,
} from "../controllers/user.controller.js";
const router = express.Router();

router.get("/", checkLogin, checkOnboardingStatus, checkIsAdmin, getAllUsers);

router.post("/create-user", checkLogin, checkOnboardingStatus, createUser);

router.delete("/delete/:id", checkLogin, checkOnboardingStatus, deleteUser);

router.put(
	"/update-user/:id",
	checkLogin,
	checkOnboardingStatus,
	updateUserFromAdmin
);

router.get("/user-profile", checkLogin, getUserProfile);

router.post(
	"/update-profile",
	checkLogin,
	upload.single("image"),
	updateProfile
);

router.get("/review-user/:userId", checkLogin, getProfileForReview);

router.get("/pending-approvals", checkLogin, getPendingApprovals);

router.get("/search", checkLogin, getSearchUser);

router.get("/search-referer-curator", checkLogin, getSearchRefererCurator);

router.get("/referer-curator-user/:userId", getRefererCuratorUser);

router.put("/change-profile-status/:userId", checkLogin, changeProfileStatus);

router.post(
	"/update-profile/:userId",
	checkLogin,
	upload.single("image"),
	updateSpecificProfileById
);

router.post(
	"/changeAdminPanelView",
	checkLogin,
	checkIsAdmin,
	changeAdminPanelView
);

export default router;
