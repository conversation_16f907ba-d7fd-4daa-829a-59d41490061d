import mongoose from "mongoose";
import User from "../models/User.js";
import bcrypt from "bcrypt";
import { rolesValues } from "../constants/index.js";
import { APP_CONFIG } from "../config/env.js";

const createAdmin = async () => {
	await mongoose.connect(APP_CONFIG.MONGO_URI);
	console.log("Connected to MongoDB");
	const email = "<EMAIL>";
	const superadmin = await User.findOne({ email: email });
	if (superadmin) {
		console.log("superadmin already exists");
		process.exit(0);
	}

	const hashedPassword = await bcrypt.hash("Holmes@221", 10);

	await User.create({
		firstName: "Arun",
		secondName: "Goyat",
		email: email,
		password: hashedPassword,
		mobile: "+917894564859",
		role: rolesValues.SuperAdmin,
	});

	console.log("superadmin created successfully");
	process.exit(0);
};

createAdmin();
