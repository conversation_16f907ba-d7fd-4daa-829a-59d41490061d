import {
	Modal,
	Table,
	Text,
	ScrollArea,
	Group,
	Stack,
	Image,
	TextInput,
} from "@mantine/core";
import type { EventUser } from "../../../types";
import { useNavigate } from "react-router-dom";
import EllipsisCell from "../../EllipsisCell";
import { useAuth } from "../../../contexts/AuthContext";
import { resolveImageUrl } from "../../../utils/imageUrl";
import FullScreenLoader from "../../FullScreenLoader";
import { useState } from "react";

interface EventAttendeesModalProps {
	opened: boolean;
	onClose: () => void;
	title: string;
	users: EventUser[];
	loading: boolean;
}

const EventAttendeesModal = ({
	opened,
	onClose,
	title,
	users,
	loading,
}: EventAttendeesModalProps) => {
	const { user } = useAuth();
	const navigate = useNavigate();

	const [searchTerm, setSearchTerm] = useState("");

	const filteredUsers = users.filter(user => {
		const fullName = `${user.firstName} ${user.secondName}`.toLowerCase();
		const search = searchTerm.toLowerCase();

		return (
			fullName.includes(search) ||
			user.email.toLowerCase().includes(search)
		);
	});

	const handleUserClick = (userId: string) => {
		if (user?._id === userId) {
			navigate("/profile");
			return;
		}
		navigate(`/search/${userId}`);
	};

	if (!opened) {
		return null;
	}

	const rows = filteredUsers.map(user => (
		<Table.Tr key={user._id}>
			<Table.Td>
				<Group
					gap="sm"
					style={{
						cursor: "pointer",
						width: "100%",
					}}
					onClick={() => handleUserClick(user._id)}
				>
					<Image
						src={resolveImageUrl(user.image)}
						alt={`${user.firstName} ${user.secondName}`}
						w={50}
						h={50}
						radius={"xl"}
						fallbackSrc={`https://api.dicebear.com/5.x/initials/svg?seed=${user.firstName} ${user.secondName}`}
					/>
					<Stack gap={0}>
						<EllipsisCell
							value={`${user.firstName} ${user.secondName}`}
							maxWidth={300}
						/>
						<Text
							fz="xs"
							c="dimmed"
							style={{
								maxWidth: 300,
								overflow: "hidden",
								textOverflow: "ellipsis",
								whiteSpace: "nowrap",
							}}
						>
							{user.email}
						</Text>
					</Stack>
				</Group>
			</Table.Td>
		</Table.Tr>
	));

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			title={title}
			size="lg"
			scrollAreaComponent={ScrollArea.Autosize}
			centered
		>
			<TextInput
				placeholder="Search by name or email"
				value={searchTerm}
				onChange={event => setSearchTerm(event.currentTarget.value)}
			/>
			<Table miw={400} verticalSpacing="sm">
				<Table.Tbody>
					{rows.length > 0 ? (
						rows
					) : (
						<Table.Tr>
							<Table.Td>
								<Text>No users found</Text>
							</Table.Td>
						</Table.Tr>
					)}
				</Table.Tbody>
				{loading && <FullScreenLoader />}
			</Table>
		</Modal>
	);
};

export default EventAttendeesModal;
