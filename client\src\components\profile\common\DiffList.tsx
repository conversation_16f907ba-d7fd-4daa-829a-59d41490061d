import { Group, Stack, Text, Divider } from "@mantine/core";
import React from "react";
import type { DiffChange } from "../../../types";
import { getChangedValue } from "../../../utils/deep-diff";
import RenderDiff from "./RenderDiff";

type DiffStatus = "unchanged" | "new" | "deleted";

interface DiffListProps<T extends object> {
	label: string;
	icon: React.ReactNode;
	data: T[] | null | undefined;
	diffChanges: DiffChange[] | null;
	pathKey: string;
	fields: {
		key: keyof T;
		props?: Partial<React.ComponentProps<typeof RenderDiff>>;
	}[];
	getStyle: (isDeleted: boolean, isNew: boolean) => React.CSSProperties;
}

export default function DiffList<T extends object>({
	label,
	icon,
	data,
	diffChanges,
	pathKey,
	fields,
	getStyle,
}: DiffListProps<T>) {
	const safeData = data ?? [];

	const itemsToRender: { item: T; status: DiffStatus }[] = safeData.map(
		d => ({ item: d, status: "unchanged" })
	);

	if (diffChanges) {
		// Handle new items
		const newDiffs = diffChanges.filter(
			c =>
				c.kind === "A" &&
				c.item?.kind === "N" &&
				c.path?.[0] === pathKey
		);

		newDiffs.forEach(diff => {
			if (typeof diff.index === "number" && diff.item?.rhs) {
				itemsToRender.splice(diff.index, 0, {
					item: diff.item.rhs as T,
					status: "new",
				});
			}
		});

		// Handle deleted items
		const deletedDiffs = diffChanges.filter(
			c =>
				(c.kind === "A" &&
					c.item?.kind === "D" &&
					c.path?.[0] === pathKey) ||
				(c.kind === "D" && c.path?.[0] === pathKey)
		);

		deletedDiffs.forEach(diff => {
			if (typeof diff.index === "number") {
				if (itemsToRender[diff.index]) {
					itemsToRender.splice(diff.index, 1);
				}
				const deletedItem = (
					diff.kind === "A" ? diff.item?.lhs : diff.lhs
				) as T | undefined;
				if (deletedItem) {
					itemsToRender.splice(diff.index, 0, {
						item: deletedItem,
						status: "deleted",
					});
				}
			}
		});
	}

	return (
		<>
			<Divider label={label} labelPosition="center" />

			{itemsToRender.length > 0 ? (
				itemsToRender.map((entry, index) => {
					const { item, status } = entry;
					const isNew = status === "new";
					const isDeleted = status === "deleted";

					return (
						<Group
							key={index}
							gap="xs"
							style={getStyle(isDeleted, isNew)}
						>
							{icon}

							<Stack gap={-4}>
								{fields.map(({ key, props }) => {
									const diff = getChangedValue(
										[pathKey, index, key as string],
										diffChanges ?? null
									);

									return diff ? (
										<RenderDiff
											key={String(key)}
											elementType="text"
											{...diff}
											{...props}
										/>
									) : (
										<RenderDiff
											key={String(key)}
											elementType="text"
											oldValue={
												(item[key] as string) || "N/A"
											}
											newValue={null}
											type={"unchanged"}
											{...props}
										/>
									);
								})}
							</Stack>
						</Group>
					);
				})
			) : (
				<Text size="sm" c="dimmed" className="pl-1">
					No {label.toLowerCase()} to display.
				</Text>
			)}
		</>
	);
}
