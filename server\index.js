import express from "express";
import cors from "cors";
import authRoutes from "./routes/auth.js";
import userRoutes from "./routes/user.js";
import userVideoRoutes from "./routes/userVideo.js";
import lifeData from "./routes/lifeData.js";
import feedbackRoutes from "./routes/feedback.js";
import eventRoutes from "./routes/event.js";
import { APP_CONFIG } from "./config/env.js";
import "./config/mongo.js";
import { initEmailService } from "./services/emaiService.js";

const app = express();
const PORT = APP_CONFIG.PORT || 5000;

app.use(express.json());
app.use("/uploads", express.static("uploads"));
app.use("/videos", express.static("uploads"));
app.use("/assets", express.static("assets"));

app.use(
	cors({
		origin: [APP_CONFIG.FRONTEND_URL],
		credentials: true,
	})
);

app.use("/api/auth", authRoutes);
app.use("/api/videos", userVideoRoutes);
app.use("/api/lifeData", lifeData);
app.use("/api/users", userRoutes);
app.use("/api/feedbacks", feedbackRoutes);
app.use("/api/events", eventRoutes);

// Initialize email service
initEmailService()
	.then(() => console.log("Email service initialized"))
	.catch(err => console.error("Failed to initialize email service:", err));

app.listen(PORT, () => console.log("Server is Running on Port " + PORT));
