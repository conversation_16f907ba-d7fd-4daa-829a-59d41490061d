import { Table, ActionIcon, Flex } from "@mantine/core";
import { IconTrash, IconPencil } from "@tabler/icons-react";
import { roleLabels, rolesLabelMap } from "../constants";
import type { UserCreation } from "../types";
import openCustomModal from "./modals/CustomModal";
import EllipsisCell from "./EllipsisCell";

interface UserTableprops {
	users: UserCreation[];
	onDelete: (userId: string) => void;
	onEdit: (user: UserCreation) => void;
	currentUserRole: number;
}

const UserTable = ({
	users,
	currentUserRole,
	onDelete,
	onEdit,
}: UserTableprops) => {
	const canDelete = currentUserRole === 1 || currentUserRole === 2;

	return (
		<Table highlightOnHover withTableBorder striped>
			<Table.Thead>
				<Table.Tr>
					<Table.Th>First Name</Table.Th>
					<Table.Th>Last Name</Table.Th>
					<Table.Th>Email</Table.Th>
					<Table.Th>Mobile</Table.Th>
					<Table.Th>Role</Table.Th>
					{canDelete && <Table.Th>Actions</Table.Th>}
				</Table.Tr>
			</Table.Thead>
			<Table.Tbody>
				{users.length > 0 ? (
					users.map(user => (
						<Table.Tr key={user._id}>
							<Table.Td>
								<EllipsisCell
									value={user.firstName}
									maxWidth={150}
								/>
							</Table.Td>
							<Table.Td>
								<EllipsisCell
									value={user.secondName}
									maxWidth={150}
								/>
							</Table.Td>
							<Table.Td>
								<EllipsisCell
									value={user.email}
									maxWidth={180}
								/>
							</Table.Td>
							<Table.Td>{user.mobile}</Table.Td>
							<Table.Td>
								{
									rolesLabelMap[
										roleLabels[
											user.role as keyof typeof roleLabels
										]
									]
								}
							</Table.Td>
							{canDelete && (
								<Table.Td>
									<Flex columnGap={12}>
										<ActionIcon
											variant="outline"
											color="blue"
											onClick={() => onEdit(user)}
										>
											<IconPencil size={14} />
										</ActionIcon>
										<ActionIcon
											variant="outline"
											color="red"
											onClick={() =>
												openCustomModal({
													confirmCallback: () =>
														onDelete(user._id),
												})
											}
										>
											<IconTrash size={14} />
										</ActionIcon>
									</Flex>
								</Table.Td>
							)}
						</Table.Tr>
					))
				) : (
					<Table.Tr>
						<Table.Td colSpan={6} style={{ textAlign: "center" }}>
							No Users Found.
						</Table.Td>
					</Table.Tr>
				)}
			</Table.Tbody>
		</Table>
	);
};

export default UserTable;
