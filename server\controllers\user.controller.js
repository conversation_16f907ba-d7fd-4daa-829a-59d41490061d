import { APP_CONFIG } from "../config/env.js";
import {
	AllProfileStatus,
	ONBOARDING_STEP,
	ProfileStatus,
	PUBLIC_NAME,
	rolesValues,
	rolesValuesNumToStr,
} from "../constants/index.js";
import Feedback from "../models/Feedback.js";
import User from "../models/User.js";
import { getPreviewVideoUrl } from "../services/awsSpace.js";
import { sendEmail } from "../services/emaiService.js";
import {
	newUserWelcomeTemplate,
	profileStatusUpdateTemplate,
	userDeletionNotificationTemplate,
} from "../utils/emailTemplates.js";
import { generateEncPassword, generatePassword } from "../utils/encryption.js";
import { cleanDeletedEmail } from "../utils/index.js";

export const getAllUsers = async (req, res) => {
	try {
		const userId = req.user._id;
		const role = req.user.role;
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 10;
		const sortField = req.query.sort || "updatedAt";
		const searchQuery = req.query.search || "";
		const skip = (page - 1) * limit;
		const sortOrder = parseInt(req.query.sortOrder) || 1;

		let query = {
			_id: { $ne: userId },
			displayStatus: true,
		};

		query.$or = [
			{ profileStatus: ProfileStatus.Approved },
			{ profileStatus: ProfileStatus.ReApproved },
		];

		if (role === rolesValues.Admin) {
			query.role = {
				$in: [rolesValues.Admin, rolesValues.CommunityMember],
			};
		}

		if (searchQuery.trim()) {
			const escapedSearchQuery = searchQuery.replace(
				/[.*+?^${}()|[\]\\]/g,
				"\\$&"
			);

			// Split search terms by whitespace
			const terms = escapedSearchQuery.split(/\s+/);

			// Build AND conditions for each term
			const andConditions = terms.map(term => {
				const regex = new RegExp(term, "i");
				return {
					$or: [
						{ firstName: regex },
						{ secondName: regex },
						{ middleName: regex },
						{ email: regex },
					],
				};
			});

			// Combine with existing $or (profile status)
			query.$and = [
				...(query.$or ? [{ $or: query.$or }] : []),
				...andConditions,
			];

			// Remove top-level $or if moved inside $and
			delete query.$or;
		}

		const [users, total] = await Promise.all([
			User.find(query)
				.collation({ locale: "en", strength: 2 })
				.sort({ [sortField]: sortOrder })
				.skip(skip)
				.limit(limit),
			User.countDocuments(query),
		]);

		if (total === 0) {
			return res.status(200).json({
				data: [],
				total: 0,
				page: 1,
				totalPages: 0,
			});
		}

		res.status(200).json({
			data: users,
			total,
			page,
			totalPages: Math.ceil(total / limit),
		});
	} catch (err) {
		console.error("Error in get-users:", err);
		res.status(500).json({ error: "Server Error" });
	}
};
export const createUser = async (req, res) => {
	const { firstName, middleName, secondName, email, role, mobile } = req.body;
	if (!rolesValuesNumToStr[role]) {
		return res.status(400).json({ message: "Invalid role" });
	}

	const alreadyExists = await User.findOne({ email });
	if (alreadyExists) {
		return res.status(400).json({ message: "User already exists" });
	}

	const password = generatePassword(8);
	const hashedPassword = generateEncPassword(password);

	const user = new User({
		firstName,
		middleName,
		secondName,
		email,
		password: hashedPassword,
		role,
		mobile,
	});
	await user.save();

	console.log(`sending email to ${email}`);
	await sendEmail({
		to: email,
		subject: `Welcome to ${PUBLIC_NAME} Gang 360 App!`,
		html: newUserWelcomeTemplate(
			firstName,
			secondName,
			email,
			password,
			APP_CONFIG.FRONTEND_URL
		),
	});
	console.log(`email sent to ${email}`);
	res.status(201).json(user);
};

export const deleteUser = async (req, res) => {
	const { id } = req.params;
	try {
		const user = await User.findById(id);
		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (user.displayStatus === false) {
			return res.status(400).json({ message: "User already deleted" });
		}
		const email = user.email;
		const timestamp = Date.now();
		user.email = `${user.email}_deleted_${timestamp}`;

		user.displayStatus = false;

		await user.save();
		res.status(200).json({ message: "User deleted successfully" });
		await sendEmail({
			to: email,
			subject: "Your account has been deleted",
			html: userDeletionNotificationTemplate(
				`${user.firstName} ${user.middleName ?? ""} ${user.secondName}`
			),
		});
	} catch (error) {
		console.log(error);
		return res.status(500).json({ message: "Failed to delete user" });
	}
};

export const updateUserFromAdmin = async (req, res) => {
	const { id } = req.params;
	const { firstName, middleName, secondName } = req.body;

	try {
		const updatedUser = await User.findByIdAndUpdate(
			id,
			{ firstName, middleName, secondName },
			{ new: true }
		);
		if (!updatedUser) {
			return res.status(404).json({ message: "User not found" });
		}
		res.status(200).json(updatedUser);
	} catch (error) {
		console.error("Error updating user:", error);
		res.status(500).json({ message: "Server Error" });
	}
};

export const getUserProfile = async (req, res) => {
	const userId = req.user._id;
	const userProfile = await User.findOne({ _id: userId });

	if (!userProfile) {
		return res.status(404).json({ message: "User not found" });
	}

	const dataToSend = {
		firstName: userProfile.firstName,
		middleName: userProfile.middleName,
		secondName: userProfile.secondName,
		// title: userProfile.title,
		image: userProfile.image,
		mobile: userProfile.mobile,
		email: userProfile.email,
		address: userProfile.address,
		// birthday: userProfile.birthday,
		city: userProfile.city,
		// gender: userProfile.gender,
		introduction: userProfile.introduction,
		quote: userProfile.quote,
		joy: userProfile.joy,
		contentLinks: userProfile.contentLinks,
		currentOrganization: userProfile.currentOrganization,
		// workplaceAddress: userProfile.workplaceAddress,
		// skills: userProfile.skills,
		twitter: userProfile.twitter,
		instagram: userProfile.instagram,
		linkedIn: userProfile.linkedIn,
		role: userProfile.role,
		otherSocialHandles: userProfile.otherSocialHandles,
		pincode: userProfile.pincode,
	};
	res.status(200).json(dataToSend);
};

export const updateProfile = async (req, res) => {
	try {
		const userId = req.user._id;
		const user = await User.findById(userId);

		const isApprovedUser =
			user.profileStatus === ProfileStatus.Approved ||
			user.profileStatus === ProfileStatus.ReApproved;

		if (!user) {
			throw new Error("User not found");
		}
		const updateData = { ...req.body };

		if (req.file) {
			updateData.image = `/uploads/image/${req.file.filename}`;
		}

		if (typeof updateData.skills === "string") {
			try {
				updateData.skills = JSON.parse(updateData.skills);
			} catch {
				updateData.skills = updateData.skills
					.split(",")
					.map(s => s.trim());
			}
		}
		if (typeof updateData.contentLinks === "string") {
			try {
				updateData.contentLinks = JSON.parse(updateData.contentLinks);
			} catch {
				updateData.contentLinks = updateData.contentLinks
					.split(",")
					.map(s => s.trim());
			}
		}
		if (typeof updateData.otherSocialHandles === "string") {
			try {
				updateData.otherSocialHandles = JSON.parse(
					updateData.otherSocialHandles
				);
			} catch {
				updateData.otherSocialHandles = updateData.otherSocialHandles
					.split(",")
					.map(s => s.trim());
			}
		}

		if (user.onboardingStep === ONBOARDING_STEP.BASIC_DETAILS) {
			updateData.onboardingStep = ONBOARDING_STEP.EARLY_LIFE_VIDEO;
		}
		const updatedUser = await User.findByIdAndUpdate(
			userId,
			{ $set: updateData },
			{ new: true }
		);

		await Feedback.updateMany(
			{
				recipient: userId,
				isActive: true,
				"feedbackMessage.basicDetails": { $exists: true },
			},
			{
				$set: { "actionTaken.basicDetails": true },
			}
		);

		res.status(200).json({
			message: "Profile updated successfully",
			user: updatedUser,
		});
	} catch (err) {
		console.log(err);
		res.status(500).json({
			message: err.message ?? "Unable to update user data",
		});
	}
};

export const getProfileForReview = async (req, res) => {
	const { userId } = req.params;
	const { view } = req.query;

	const viewMap = {
		searchView: [
			"basicDetails",
			"earlyLifeData",
			"professionalLifeData",
			"currentLifeData",
		],
		profileReviewView: [
			"basicDetails",
			"mobile",
			"earlyLifeData",
			"professionalLifeData",
			"currentLifeData",
		],
	};

	if (!view || !viewMap[view]) {
		console.error("Invalid view parameter", view);
		return res.status(400).json({ message: "Something went wrong" });
	}

	if (view === "searchView" && userId === req.user._id.toString()) {
		return res
			.status(400)
			.json({ message: "You cannot view your own profile" });
	}

	const isProfileReviewView = view === "profileReviewView";

	const selectedFields = viewMap[view] || viewMap.searchView;

	try {
		const user = await User.findById(userId);
		if (user.displayStatus === false) {
			return res.status(404).json({ message: "User not found" });
		}
		if (
			view === "searchView" &&
			!(
				user.profileStatus === ProfileStatus.Approved ||
				user.profileStatus === ProfileStatus.ReApproved
			)
		) {
			return res
				.status(400)
				.json({ message: "User profile is not approved yet" });
		}
		if (
			isProfileReviewView &&
			req.user.role === rolesValues.Admin &&
			user.role === rolesValues.SuperAdmin
		) {
			return res
				.status(400)
				.json({ message: "You cannot view super admin profile" });
		}
		if (
			isProfileReviewView &&
			user.profileStatus === ProfileStatus.Onboarding
		) {
			return res
				.status(400)
				.json({ message: "User not submitted for review yet" });
		}
		const isAfterApproved =
			user.profileStatus === ProfileStatus.Approved ||
			user.profileStatus === ProfileStatus.ReApproved;

		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (
			isProfileReviewView &&
			user.profileStatus === ProfileStatus.Approved
		) {
			return res
				.status(400)
				.json({ message: "User profile is already approved" });
		}

		let earlyLifeData = user.earlyLifeData;
		let professionalLifeData = user.professionalLifeData;
		let currentLifeData = user.currentLifeData;

		let earlyLifeVideoPath = null;
		if (selectedFields.includes("earlyLifeData")) {
			if (earlyLifeData && earlyLifeData.videoId) {
				earlyLifeVideoPath = await getPreviewVideoUrl(
					userId,
					earlyLifeData.videoId
				);
			}
		}
		let professionalLifeVideoPath = null;
		if (selectedFields.includes("professionalLifeData")) {
			if (professionalLifeData && professionalLifeData.videoId) {
				professionalLifeVideoPath = await getPreviewVideoUrl(
					userId,
					professionalLifeData.videoId
				);
			}
		}
		let currentLifeVideoPath = null;
		if (selectedFields.includes("currentLifeData")) {
			if (currentLifeData && currentLifeData.videoId) {
				currentLifeVideoPath = await getPreviewVideoUrl(
					userId,
					currentLifeData.videoId
				);
			}
		}

		let dataToSend = {};

		if (selectedFields.includes("basicDetails")) {
			dataToSend.basicDetails = {
				firstName: user.firstName,
				middleName: user.middleName,
				secondName: user.secondName,
				email: user.email,
				role: user.role,
				image: user.image,
				// title: user.title,
				address: user.address,
				// birthday: user.birthday,
				city: user.city,
				// gender: user.gender,
				introduction: user.introduction,
				quote: user.quote,
				joy: user.joy,
				contentLinks: user.contentLinks,
				currentOrganization: user.currentOrganization,
				// workplaceName: user.workplaceName,
				// workplaceAddress: user.workplaceAddress,
				// skills: user.skills,
				twitter: user.twitter,
				instagram: user.instagram,
				linkedIn: user.linkedIn,
				otherSocialHandles: user.otherSocialHandles,
				pincode: user.pincode,
			};
			if (selectedFields.includes("mobile")) {
				dataToSend.basicDetails.mobile = user.mobile;
			}
		}

		if (isProfileReviewView && isAfterApproved) {
			if (user.updatedEarlyLifeData) {
				dataToSend.updatedEarlyLifeData = {
					birthCity: user.updatedEarlyLifeData.birthCity,
					hometownCity: user.updatedEarlyLifeData.hometownCity,
					universities: user.updatedEarlyLifeData.universities,
					schools: user.updatedEarlyLifeData.schools,
					earlyLifeTags: user.updatedEarlyLifeData.earlyLifeTags,
					earlyLifeSummary:
						user.updatedEarlyLifeData.earlyLifeSummary,
					videoUrl: earlyLifeVideoPath,
				};
			}
			if (user.updatedProfessionalLifeData) {
				dataToSend.updatedProfessionalLifeData = {
					firstJob: user.updatedProfessionalLifeData.firstJob,
					subsequentJobs:
						user.updatedProfessionalLifeData.subsequentJobs,
					professionalLifeTags:
						user.updatedProfessionalLifeData.professionalLifeTags,
					professionalLifeSummary:
						user.updatedProfessionalLifeData
							.professionalLifeSummary,
					videoUrl: professionalLifeVideoPath,
				};
			}
			if (user.updatedCurrentLifeData) {
				dataToSend.updatedCurrentLifeData = {
					currentLifeSummary:
						user.updatedCurrentLifeData.currentLifeSummary,
					currentCities: user.updatedCurrentLifeData.currentCities,
					currentOrganizations:
						user.updatedCurrentLifeData.currentOrganizations,
					frequentTravelCities:
						user.updatedCurrentLifeData.frequentTravelCities,
					currentLifeTags:
						user.updatedCurrentLifeData.currentLifeTags,
					videoUrl: currentLifeVideoPath,
				};
			}
		}

		if (selectedFields.includes("earlyLifeData")) {
			dataToSend.earlyLifeData = earlyLifeData
				? {
						birthCity: earlyLifeData.birthCity,
						hometownCity: earlyLifeData.hometownCity,
						universities: earlyLifeData.universities,
						schools: earlyLifeData.schools,
						earlyLifeTags: earlyLifeData.earlyLifeTags,
						earlyLifeSummary: earlyLifeData.earlyLifeSummary,
						videoUrl: earlyLifeVideoPath,
					}
				: null;
		}

		if (selectedFields.includes("professionalLifeData")) {
			dataToSend.professionalLifeData = professionalLifeData
				? {
						firstJob: professionalLifeData.firstJob,
						subsequentJobs: professionalLifeData.subsequentJobs,
						professionalLifeTags:
							professionalLifeData.professionalLifeTags,
						professionalLifeSummary:
							professionalLifeData.professionalLifeSummary,
						videoUrl: professionalLifeVideoPath,
					}
				: null;
		}

		if (selectedFields.includes("currentLifeData")) {
			dataToSend.currentLifeData = currentLifeData
				? {
						currentLifeSummary: currentLifeData.currentLifeSummary,
						currentCities: currentLifeData.currentCities,
						currentOrganizations:
							currentLifeData.currentOrganizations,
						frequentTravelCities:
							currentLifeData.frequentTravelCities,
						currentLifeTags: currentLifeData.currentLifeTags,
						videoUrl: currentLifeVideoPath,
					}
				: null;
		}

		dataToSend.profileStatus = user.profileStatus;
		dataToSend.onboardingStep = user.onboardingStep;

		if (isProfileReviewView) {
			const reviewerId = req.user._id;
			const feedbackCount = await Feedback.countDocuments({
				_id: { $in: user.feedbacks },
				isActive: true,
				readBy: { $nin: [reviewerId] },
			});
			dataToSend.feedbackCount = feedbackCount || 0;
		}
		return res.status(200).json(dataToSend);
	} catch (error) {
		console.error("Error retrieving user:", error);
		res.status(500).json({ message: "Server Error" });
	}
};

export const getPendingApprovals = async (req, res) => {
	try {
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 10;
		const sortField = req.query.sort || "updatedAt";
		const skip = (page - 1) * limit;
		const searchQuery = req.query.search || "";
		const sortOrder = parseInt(req.query.sortOrder) || 1;
		let profileStatus = req.query.profileStatus;

		if (!AllProfileStatus.includes(profileStatus)) {
			profileStatus = "all";
		}

		const query = {
			displayStatus: true,
		};

		if (req.user.role === rolesValues.Admin) {
			query.role = {
				$in: [rolesValues.Admin, rolesValues.CommunityMember],
			};
		}
		if (profileStatus === ProfileStatus.Onboarding) {
			query.profileStatus = ProfileStatus.Onboarding;
		} else if (profileStatus === ProfileStatus.Pending) {
			query.profileStatus = ProfileStatus.Pending;
		} else if (profileStatus === ProfileStatus.ReApproved) {
			query.profileStatus = ProfileStatus.ReApproved;
		} else if (profileStatus === ProfileStatus.ChangesRequested) {
			query.profileStatus = ProfileStatus.ChangesRequested;
		} else if (profileStatus === "all") {
			query.$or = [{ profileStatus: { $ne: ProfileStatus.Approved } }];
		}

		if (searchQuery.trim()) {
			const escapedSearchQuery = searchQuery.replace(
				/[.*+?^${}()|[\]\\]/g,
				"\\$&"
			);

			// Split search terms by whitespace
			const terms = escapedSearchQuery.split(/\s+/);

			// Build AND conditions for each term
			const andConditions = terms.map(term => {
				const regex = new RegExp(term, "i");
				return {
					$or: [
						{ firstName: regex },
						{ secondName: regex },
						{ middleName: regex },
						{ email: regex },
					],
				};
			});

			// Combine with existing $or (profile status)
			query.$and = [
				...(query.$or ? [{ $or: query.$or }] : []),
				...andConditions,
			];

			// Remove top-level $or if moved inside $and
			delete query.$or;
		}

		const total = await User.countDocuments(query);

		const users = await User.aggregate([
			{ $match: query },
			{ $sort: { [sortField]: sortOrder } },
			{ $skip: skip },
			{ $limit: parseInt(limit) },
			{
				$lookup: {
					from: "feedbacks",
					let: { userId: "$_id" },
					pipeline: [
						{
							$match: {
								$expr: {
									$and: [
										{ $ne: ["$recipient", req.user._id] },
										{ $eq: ["$recipient", "$$userId"] },
										{ $eq: ["$isActive", true] },
									],
								},
							},
						},
						{ $sort: { createdAt: -1 } },
						{
							$project: {
								_id: 1,
								feedbackMessage: 1,
								actionTaken: 1,
								isRead: { $in: ["$$userId", "$readBy"] },
							},
						},
					],
					as: "activeFeedbacks",
				},
			},
			{
				$project: {
					firstName: 1,
					secondName: 1,
					email: 1,
					mobile: 1,
					role: 1,
					profileStatus: 1,
					onboardingStep: 1,
					resendToOnboarding: 1,
					activeFeedbacks: {
						$cond: {
							if: { $eq: [{ $size: "$activeFeedbacks" }, 0] },
							then: "$$REMOVE",
							else: "$activeFeedbacks",
						},
					},
					unreadFeedbacks: {
						$cond: {
							if: { $eq: [{ $size: "$activeFeedbacks" }, 0] },
							then: "$$REMOVE",
							else: {
								$size: {
									$filter: {
										input: "$activeFeedbacks",
										as: "feedback",
										cond: {
											$eq: ["$$feedback.isRead", false],
										},
									},
								},
							},
						},
					},
					actionCompleted: {
						$cond: {
							if: { $eq: [{ $size: "$activeFeedbacks" }, 0] },
							then: "$$REMOVE",
							else: {
								$allElementsTrue: [
									{
										$map: {
											input: "$activeFeedbacks",
											as: "feedback",
											in: {
												$not: {
													$in: [
														false,
														{
															$map: {
																input: {
																	$objectToArray:
																		{
																			$ifNull:
																				[
																					"$$feedback.actionTaken",
																					{},
																				],
																		},
																},
																as: "action",
																in: "$$action.v",
															},
														},
													],
												},
											},
										},
									},
								],
							},
						},
					},
				},
			},
		]);

		res.status(200).json({
			data: users,
			total,
			page,
			totalPages: Math.ceil(total / limit),
		});
	} catch (err) {
		console.error("Error in get-pending-profiles:", err);
		res.status(500).json({ error: "Server Error" });
	}
};

export const updateSpecificProfileById = async (req, res) => {
	try {
		const { userId } = req.params;
		const user = await User.findById(userId);

		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (user.displayStatus === false) {
			return res.status(404).json({ message: "User not found" });
		}
		const updateData = {
			firstName: req.body.firstName?.trim() || "",
			secondName: req.body.secondName?.trim() || "",
			middleName: req.body.middleName?.trim() || "",
			address: req.body.address?.trim() || "",
			city: req.body.city?.trim() || "",
			introduction: req.body.introduction?.trim() || "",
			quote: req.body.quote?.trim() || "",
			joy: req.body.joy?.trim() || "",
			currentOrganization: req.body.currentOrganization?.trim() || "",
			twitter: req.body.twitter?.trim() || "",
			instagram: req.body.instagram?.trim() || "",
			linkedIn: req.body.linkedIn?.trim() || "",
		};

		if (req.file) {
			updateData.image = `/uploads/image/${req.file.filename}`;
		}

		["contentLinks", "otherSocialHandles"].forEach(key => {
			const value = req.body[key];
			if (typeof value === "string") {
				try {
					updateData[key] = JSON.parse(value);
				} catch {
					updateData[key] = value
						.split(",")
						.map(s => s.trim())
						.filter(s => s !== "");
				}
			} else if (Array.isArray(value)) {
				updateData[key] = value.filter(s => s.trim() !== "");
			} else {
				updateData[key] = [];
			}
		});

		const updatedUser = await User.findByIdAndUpdate(
			userId,
			{ $set: updateData },
			{ new: true }
		);

		res.status(200).json({
			message: "Profile updated successfully",
			user: updatedUser,
		});
	} catch (err) {
		console.error(err);
		res.status(500).json({
			message: err.message ?? "Unable to update user data",
		});
	}
};

export const getSearchUser = async (req, res) => {
	try {
		const { query } = req.query;
		if (!query) {
			return res
				.status(400)
				.json({ message: "Search query is required" });
		}

		const escapedQuery = query
			.trim()
			.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

		const terms = escapedQuery.split(/\s+/);

		const searchConditions = terms.map(term => {
			const regex = new RegExp(term, "i");
			return {
				$or: [
					{ firstName: regex },
					{ middleName: regex },
					{ secondName: regex },
					{ email: regex },
					{ "earlyLifeData.earlyLifeTags": regex },
					{ "professionalLifeData.professionalLifeTags": regex },
					{ "currentLifeData.currentLifeTags": regex },
				],
			};
		});

		const users = await User.find({
			$and: [
				{ _id: { $ne: req.user._id } },
				{
					profileStatus: {
						$in: [ProfileStatus.Approved, ProfileStatus.ReApproved],
					},
				},
				{ displayStatus: true },
				...searchConditions,
			],
		}).select("firstName middleName secondName image email role");

		res.status(200).json(users);
	} catch (error) {
		console.error("Error searching users:", error);
		res.status(500).json({ message: "Server Error" });
	}
};

function mergeLifeData(original, updated) {
	if (!updated) return original;
	return {
		...(original.toObject?.() || original),
		...(updated.toObject?.() || updated),
		videoId: original?.videoId || updated?.videoId || null,
	};
}

export const changeProfileStatus = async (req, res) => {
	const { userId } = req.params;
	const { status, refererId, curatorId } = req.body;

	if (!status) {
		return res.status(400).json({ message: "Status is required" });
	}

	const user = await User.findById(userId);
	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}
	if (user.displayStatus === false) {
		return res.status(404).json({ message: "User not found" });
	}
	if (user.onboardingStep < ONBOARDING_STEP.WAIT_FOR_APPROVAL) {
		return res
			.status(400)
			.json({ message: "User profile is not yet submitted" });
	}

	let newStatus = status;
	if (
		status === ProfileStatus.Approved ||
		status === ProfileStatus.ReApproved
	) {
		newStatus = ProfileStatus.Approved;
	}
	try {
		const updatePayload = {
			profileStatus: newStatus,
		};
		const unsetPayload = {};

		if (status === ProfileStatus.Approved) {
			if (!refererId || !curatorId) {
				return res.status(400).json({
					message: "Referer and Curator are required for approval",
				});
			}
			updatePayload.onboardingStep = ONBOARDING_STEP.COMPLETED;
			updatePayload.referer = refererId;
			updatePayload.curator = curatorId;
		} else if (status === ProfileStatus.ReApproved) {
			if (user.updatedEarlyLifeData) {
				updatePayload.earlyLifeData = mergeLifeData(
					user.earlyLifeData,
					user.updatedEarlyLifeData
				);
				unsetPayload.updatedEarlyLifeData = 1;
			}
			if (user.updatedProfessionalLifeData) {
				updatePayload.professionalLifeData = mergeLifeData(
					user.professionalLifeData,
					user.updatedProfessionalLifeData
				);
				unsetPayload.updatedProfessionalLifeData = 1;
			}
			if (user.updatedCurrentLifeData) {
				updatePayload.currentLifeData = mergeLifeData(
					user.currentLifeData,
					user.updatedCurrentLifeData
				);
				unsetPayload.updatedCurrentLifeData = 1;
			}
		}
		updatePayload.approvedBy = [
			...user.approvedBy,
			{ user: req.user._id, date: Date.now() },
		];

		updatePayload.resendToOnboarding = false;

		await Feedback.updateMany(
			{ _id: { $in: user.feedbacks } },
			{ $set: { isActive: false } }
		);

		const updatedUser = await User.findByIdAndUpdate(
			userId,
			{
				$set: updatePayload,
				...(Object.keys(unsetPayload).length
					? { $unset: unsetPayload }
					: {}),
			},
			{ new: true }
		);
		if (!updatedUser) {
			return res.status(404).json({ message: "User not found" });
		}

		const userEmail = updatedUser.email;
		const userName = `${updatedUser.firstName} ${updatedUser.secondName}`;
		const frontendBaseUrl = APP_CONFIG.FRONTEND_URL;
		const profileLink = `${frontendBaseUrl}/profile/basic-details`;

		res.status(200).json({ message: `User ${status} successfully` });
		await sendEmail({
			to: userEmail,
			subject: `${PUBLIC_NAME} Community Profile Approved`,
			html: profileStatusUpdateTemplate(userName, status, profileLink),
		});
	} catch (error) {
		console.error(`Error in ${status} user:`, error);
		res.status(500).json({ message: "Server Error" });
	}
};

export const getSearchRefererCurator = async (req, res) => {
	try {
		const { query } = req.query;
		if (!query) {
			return res
				.status(400)
				.json({ message: "Search query is required" });
		}

		const escapedQuery = query
			.trim()
			.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

		const users = await User.find({
			$and: [
				{
					profileStatus: {
						$in: [ProfileStatus.Approved, ProfileStatus.ReApproved],
					},
				},
				{ displayStatus: true },
				{
					$or: [
						{ firstName: { $regex: escapedQuery, $options: "i" } },
						{ secondName: { $regex: escapedQuery, $options: "i" } },
						{ email: { $regex: escapedQuery, $options: "i" } },
					],
				},
			],
		}).select("firstName secondName image email role profileStatus");

		res.status(200).json(users);
	} catch (error) {
		console.error("Error searching users:", error);
		res.status(500).json({ message: "Server Error" });
	}
};

export const getRefererCuratorUser = async (req, res) => {
	try {
		const { userId } = req.params;
		const user = await User.findById(userId);

		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (user.displayStatus === false) {
			return res.status(404).json({ message: "User not found" });
		}
		const refererId = user.referer;
		const curatorId = user.curator;
		const refererUser = await User.findById(refererId);
		const curatorUser = await User.findById(curatorId);

		res.status(200).json({
			referer: {
				email: refererUser.displayStatus
					? refererUser.email
					: cleanDeletedEmail(refererUser.email),
				_id: refererUser._id,
			},
			curator: {
				email: curatorUser.displayStatus
					? curatorUser.email
					: cleanDeletedEmail(curatorUser.email),
				_id: curatorUser._id,
			},
		});
	} catch (err) {
		console.error("Error in get referer carutor user:", err);
		res.status(500).json({ error: "Server Error" });
	}
};

export const changeAdminPanelView = async (req, res) => {
	try {
		const user = req.user;
		user.isAdminPanelUser = !user.isAdminPanelUser;
		await user.save();
		const message = user.isAdminPanelUser
			? "Signed in as admin successfully"
			: "Successfully exited the admin panel.";
		res.status(200).json({ message });
	} catch (error) {
		console.error("Error in signInAsAdmin:", error);
		res.status(500).json({ message: "Server error" });
	}
};
