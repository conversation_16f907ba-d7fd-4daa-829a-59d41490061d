import React, { useState } from "react";
import EarlyLifePreview from "../profile/EarlyLifePreview";
import EarlyLifeForm from "../onBoarding-forms/EarlyLifeForm";
import type { EarlyLifeDataType } from "../../types";

interface EarlyLifeProps {
	earlyLifeData: EarlyLifeDataType;
	fetchProfile: () => void;
	userId?: string;
	updatedEarlyLifeData?: EarlyLifeDataType;
	isEditable?: boolean;
}

const EarlyLife: React.FC<EarlyLifeProps> = ({
	earlyLifeData,
	fetchProfile,
	userId,
	updatedEarlyLifeData,
	isEditable,
}) => {
	const [editing, setEditing] = useState(false);
	return (
		<>
			{editing ? (
				<>
					<EarlyLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={updatedEarlyLifeData ?? earlyLifeData}
						userId={userId}
					/>
				</>
			) : (
				<EarlyLifePreview
					showEdit={isEditable ?? true}
					setEditing={setEditing}
					lifeData={earlyLifeData}
					updatedLifeData={updatedEarlyLifeData}
					userId={userId}
				/>
			)}
		</>
	);
};

export default EarlyLife;
