import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import FullScreenLoader from "./FullScreenLoader";
import { useEffect } from "react";

const SwitchingPanel = () => {
	const { user, isUpdatingUser, isSwitchingAdminPanel } = useAuth();
	const navigate = useNavigate();

	useEffect(() => {
		if (user && !isUpdatingUser && !isSwitchingAdminPanel) {
			if (user.isAdminPanelUser) {
				navigate("/profile", { replace: true });
			} else {
				navigate("/", { replace: true });
			}
		} else {
			console.log(
				"SwitchingPanel: Waiting for user data or updates to complete",
				{
					hasUser: !!user,
					isUpdatingUser,
					isSwitchingAdminPanel,
				}
			);
		}
	}, [user, navigate, isUpdatingUser, isSwitchingAdminPanel]);

	return <FullScreenLoader />;
};

export default SwitchingPanel;
