import mongoose from "mongoose";
import User from "../models/User.js";
import { APP_CONFIG } from "../config/env.js";
import fs from "fs";
import path from "path";
import { ONBOARDING_STEP, ProfileStatus } from "../constants/index.js";

const migrateOnboardingStep5 = async () => {
	try {
		const superadmin = await User.findOne({
			email: "<EMAIL>",
		});
		if (!superadmin) throw new Error("Superadmin not found");

		const result = await User.updateMany(
			{ onboardingStep: 5, profileStatus: "approved" },
			{
				$set: {
					onboardingStep: 10,
					profileStatus: "approved",
					referer: superadmin._id,
					curator: superadmin._id,
					address: "Bangalore",
					city: "Bangalore",
					introduction:
						"Living in Bangalore, working with Microsoft as a product engineer focused on cloud solutions, and mentoring remotely at Codebloom. Balances innovation, growth, and wellness in a fast-paced, tech-driven, globally connected environment.",
					quote: "fun",
					joy: "joy",
					currentOrganization: "360",
					displayStatus: true,
					instagram: "",
					twitter: "",
					linkedIn: "",
				},
			}
		);

		console.log("Migrated users:", result.modifiedCount);
	} catch (error) {
		console.error(error);
	}
};

const changeDisplayStatus = async () => {
	try {
		const result = await User.updateMany(
			{ displayStatus: { $exists: false } },
			{ $set: { displayStatus: true } }
		);
		console.log("Display status updated for:", result.modifiedCount);
	} catch (error) {
		console.error(error);
	}
};

const migrateOneUser = async () => {
	try {
		const result = await User.findOneAndUpdate(
			{ email: "<EMAIL>", onboardingStep: 1 },
			{ $set: { onboardingStep: 5 } },
			{ new: true }
		);

		if (result) {
			console.log(
				"Changed onboardingStep for",
				result.email,
				"to",
				result.onboardingStep
			);
		} else {
			console.log("User not found");
		}
	} catch (error) {
		console.error(error);
	}
};

const migrateImagePath = async () => {
	try {
		const users = await User.find();

		for (const user of users) {
			if (user.image && user.image.includes("/uploads/video/")) {
				// Convert relative DB path to absolute
				const oldPath = path.join(process.cwd(), user.image);

				// Build new DB path
				const newImagePath = user.image.replace(
					"/uploads/video/",
					"/uploads/image/"
				);

				// Build absolute new file path
				const newPath = path.join(process.cwd(), newImagePath);

				// Ensure directory exists
				const newDir = path.dirname(newPath);
				if (!fs.existsSync(newDir)) {
					fs.mkdirSync(newDir, { recursive: true });
				}

				// Move file if exists
				if (fs.existsSync(oldPath)) {
					fs.renameSync(oldPath, newPath);
					console.log(`📂 Moved file: ${oldPath} -> ${newPath}`);
				} else {
					console.warn(`⚠️ File not found: ${oldPath}`);
				}

				// Update DB
				user.image = newImagePath;
				await user.save();
				console.log(`✅ Updated DB for user ${user._id}`);
			}
		}

		console.log("🎉 Migration complete");
	} catch (error) {
		console.error("❌ Migration failed", error);
	}
};

const addIdField = async () => {
	try {
		const users = await User.find({
			displayStatus: { $ne: false },
		});
		for (const user of users) {
			delete user._id;
			await User.updateOne({ email: user.email }, { $set: { ...user } });
			console.log(`✅ Updated DB for user ${user.email}`);
		}
	} catch (error) {
		console.error("❌ Migration failed for addIdField", error);
	}
};

const addRefererCurator = async () => {
	try {
		const superadmin = await User.findOne({
			email: "<EMAIL>",
		});

		// 3. Update all users that are missing referer/curator
		const result = await User.updateMany(
			{
				$or: [
					{ referer: { $exists: false } },
					{ curator: { $exists: false } },
					{ referer: null },
					{ curator: null },
				],
			},
			{
				$set: { referer: superadmin._id, curator: superadmin._id },
			}
		);

		console.log(`✅ Updated ${result.modifiedCount} users.`);
	} catch (error) {
		console.error("❌ Error updating users:", error);
	}
};

const migrateProfileStatusPendingToOnboarding = async () => {
	try {
		const result = await User.updateMany(
			{
				profileStatus: ProfileStatus.Pending,
				onboardingStep: { $lt: ONBOARDING_STEP.WAIT_FOR_APPROVAL },
			},
			{ $set: { profileStatus: ProfileStatus.Onboarding } }
		);
		console.log(`Updated ${result.modifiedCount} users.`);
	} catch (error) {
		console.error("❌ Migration failed", error);
	}
};

const runMigration = async version => {
	try {
		await mongoose.connect(APP_CONFIG.MONGO_URI);
		console.log("Version:", version);
		switch (version) {
			case "1.0.4":
				await migrateProfileStatusPendingToOnboarding();
				break;
			case "1.0.3":
				await addRefererCurator();
				break;
			case "1.0.2":
				await addIdField();
				break;
			case "1.0.1":
				await changeDisplayStatus();
				await migrateOneUser();
				await migrateImagePath();
				await migrateOnboardingStep5();
				break;
			default:
				console.log("No migration found for version", version);
				break;
		}

		process.exit(0);
	} catch (error) {
		console.log(error);
		process.exit(1);
	}
};

const version = "1.0.4";
runMigration(version);
