import mongoose from "mongoose";

// * * EVENT POST SCHEMA
const eventPostSchema = new mongoose.Schema(
	{
		eventId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "Event",
			required: true,
		},
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},
		content: { type: String, trim: true },
		userTags: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
		media: [{ type: mongoose.Schema.Types.ObjectId, ref: "Media" }],

		isDeleted: { type: Boolean, default: false },

		approved: {
			type: String,
			enum: ["pending", "approved"],
			default: "pending",
		},
		approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
		approvedAt: { type: Date },
	},
	{ _id: true, timestamps: true }
);

// * * MEDIA SCHEMA
const mediaSchema = new mongoose.Schema(
	{
		S3Key: { type: String, required: true }, // S3 key
		key: { type: String, required: true }, // unique key for the media
		type: { type: String, enum: ["image", "video"], required: true },

		uploadedBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},

		// Admin approval flow
		approved: {
			type: String,
			enum: ["pending", "approved"],
			default: "pending",
		},
		approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
		approvedAt: { type: Date },

		// Thumbnail chosen by admin
		isThumbnail: { type: Boolean, default: false },

		// caption: { type: String, trim: true },

		// ✅ Users tagged in this media (photos/videos)
		userTags: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],

		// To differentiate uploads
		source: {
			type: String,
			enum: ["creation", "community"],
			default: "community",
		},

		isDeleted: { type: Boolean, default: false },
	},

	{ _id: true, timestamps: true }
);

// * * COMMENT SCHEMA
const commentSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},
		text: { type: String, required: true, trim: true },
		createdAt: { type: Date, default: Date.now },

		// Optional: link comment to specific media
		mediaId: { type: mongoose.Schema.Types.ObjectId, ref: "Media" },

		// ✅ Mentions in comment text (e.g. @username)
		mentionedUsers: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
	},
	{ _id: true, timestamps: true }
);

// * * ATTENDANCE SCHEMA (embedded)
const attendanceSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},
		status: {
			type: String,
			enum: ["attending", "not_attending"],
			default: "attending",
		},
	},
	{ _id: false, timestamps: true }
);

// * * EVENT SCHEMA
const eventSchema = new mongoose.Schema(
	{
		name: { type: String, required: true, trim: true, index: true },
		description: { type: String, required: true, trim: true },
		startDate: { type: Date, required: true, index: true },
		endDate: { type: Date, required: true, index: true },
		detail: { type: String },
		createdBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},

		thumbnailImage: { type: mongoose.Schema.Types.ObjectId, ref: "Media" },

		// ✅ Event creator media (admin uploads at creation)
		creationMedia: [{ type: mongoose.Schema.Types.ObjectId, ref: "Media" }],

		eventPosts: [
			{ type: mongoose.Schema.Types.ObjectId, ref: "EventPost" },
		],

		// ✅ Attendees
		attendees: [attendanceSchema],

		// ✅ Comments (references for scalability)
		comments: [{ type: mongoose.Schema.Types.ObjectId, ref: "Comment" }],

		// Admin controls
		status: {
			type: String,
			enum: ["published", "unpublished"],
			default: "unpublished",
			index: true,
		},

		// approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
		// approvedAt: { type: Date },

		// Quick counters
		counts: {
			totalMedia: { type: Number, default: 0 },
			approvedMedia: { type: Number, default: 0 },
			attendeesCount: { type: Number, default: 0 },
			nonAttendingCount: { type: Number, default: 0 },
			commentsCount: { type: Number, default: 0 },
		},

		isDeleted: { type: Boolean, default: false },
	},
	{
		timestamps: true,
	}
);

// * * Auto-update timestamp
eventSchema.pre("save", function (next) {
	this.updatedAt = Date.now();
	next();
});

// * * Indexes
eventSchema.index({ startDate: 1, endDate: 1 });
eventSchema.index({ "counts.totalMedia": -1 });
eventSchema.index({ name: "text", description: "text" });

// * * EXPORT MODELS
export const Media = mongoose.model("Media", mediaSchema);
export const Comment = mongoose.model("Comment", commentSchema);
export const Event = mongoose.model("Event", eventSchema);
export const EventPost = mongoose.model("EventPost", eventPostSchema);
