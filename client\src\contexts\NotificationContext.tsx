import { createContext, useState, useContext, useCallback } from "react";
import type { ReactNode } from "react";
import apiClient from "../config/axios";
import type {
	ActionInfoType,
	FeedbackDataType,
	NotificationContextType,
	NotificationCounts,
} from "../types";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";

const NotificationContext = createContext<NotificationContextType | null>(null);

// eslint-disable-next-line react-refresh/only-export-components
export const useNotifications = () => {
	const context = useContext(NotificationContext);
	if (!context) {
		throw new Error(
			"useNotifications must be used within a NotificationProvider"
		);
	}
	return context;
};

interface NotificationProviderProps {
	children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
	const [feedbacks, setFeedbacks] = useState<FeedbackDataType[]>([]);
	const [actionInfo, setActionInfo] = useState<ActionInfoType>({});
	const [counts, setCounts] = useState<NotificationCounts>({
		totalCount: 0,
		unreadCount: 0,
	});
	const [loading, setLoading] = useState(false);
	const [viewMoreLoading, setViewMoreLoading] = useState(false);
	const [notificationsOpened, setNotificationsOpened] =
		useState<boolean>(false);

	const toggleNotifications = useCallback(() => {
		setNotificationsOpened(prev => !prev);
	}, []);

	const fetchFeedbacks = useCallback(
		async (isViewMore?: boolean, current_count?: number) => {
			if (isViewMore) {
				setViewMoreLoading(true);
			} else {
				setLoading(true);
			}
			try {
				const response = await apiClient.get(
					`/api/feedbacks/notifications?isViewMore=${isViewMore}&current_count=${current_count}`
				);
				setFeedbacks(prevFeedbacks => {
					const feedbackSet = new Set(
						(prevFeedbacks ?? []).map(feedback => feedback._id)
					);
					const newFeedbacks = response.data.data.filter(
						(feedback: FeedbackDataType) =>
							!feedbackSet.has(feedback._id)
					);
					return [...prevFeedbacks, ...newFeedbacks];
				});
				setCounts({
					totalCount: response.data.total,
					unreadCount: response.data.unreadCount,
				});
			} catch (error) {
				console.error(error);
				notifications.show({
					title: "Failed",
					message: isAxiosError(error)
						? error.response?.data?.message ||
							"Failed to fetch feedbacks"
						: "Failed to fetch feedbacks",
					color: "red",
				});
			} finally {
				if (isViewMore) {
					setViewMoreLoading(false);
				} else {
					setLoading(false);
				}
			}
		},
		[]
	);

	const loadMoreNotifications = useCallback(() => {
		if (feedbacks.length < counts.totalCount && !viewMoreLoading) {
			fetchFeedbacks(true, feedbacks.length);
		}
	}, [feedbacks.length, counts.totalCount, viewMoreLoading, fetchFeedbacks]);

	const decrementUnreadCount = useCallback(() => {
		setCounts(prev => ({
			...prev,
			unreadCount: Math.max(0, prev.unreadCount - 1),
		}));
	}, []);

	const getActionInfo = useCallback(async () => {
		try {
			const response = await apiClient.get("/api/feedbacks/action-info");
			setActionInfo(response.data.actionInfo);
		} catch (error) {
			console.error(error);
			return {};
		}
	}, []);

	const value: NotificationContextType = {
		feedbacks,
		counts,
		loading,
		viewMoreLoading,
		notificationsOpened,
		toggleNotifications,
		fetchFeedbacks,
		loadMoreNotifications,
		decrementUnreadCount,
		getActionInfo,
		actionInfo,
	};

	return (
		<NotificationContext.Provider value={value}>
			{children}
		</NotificationContext.Provider>
	);
}
