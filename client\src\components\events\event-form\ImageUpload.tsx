import {
	<PERSON><PERSON>,
	<PERSON>,
	Flex,
	Group,
	Stack,
	Text,
	Title,
	useMantineTheme,
} from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { ImageIcon } from "lucide-react";
import React, { useCallback, useEffect } from "react";
import { IconX } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { getFileKey } from "../../../utils";
import EventImageCards from "../EventImageCards";
import type { EventDetailsType } from "../../../types";

export type ImageUploadProps = {
	images: (File | EventDetailsType["creationMedia"][0])[];
	setImages: React.Dispatch<
		React.SetStateAction<(File | EventDetailsType["creationMedia"][0])[]>
	>;
	thumbnailImage: File | EventDetailsType["creationMedia"][0] | null;
	setThumbnailImage: (
		file: File | EventDetailsType["creationMedia"][0] | null
	) => void;
};

const getImageKey = (img: File | EventDetailsType["creationMedia"][0]) => {
	if (img instanceof File) {
		return getFileKey(img);
	}
	return img.key;
};

// Use this for UI selection where components expect an id
const getImageId = (img: File | EventDetailsType["creationMedia"][0]) => {
	if (img instanceof File) {
		return getFileKey(img);
	}
	return img._id;
};

const ImageUpload = ({
	images,
	setImages,
	thumbnailImage,
	setThumbnailImage,
}: ImageUploadProps) => {
	const theme = useMantineTheme();

	const handleDrop = useCallback(
		(files: File[]) => {
			setImages(prev => {
				const existingKeys = new Set(prev.map(img => getImageKey(img)));

				let haveDuplicateImages = false;
				const newFiles = files.filter(file => {
					const fileKey = getFileKey(file);

					if (existingKeys.has(fileKey)) {
						haveDuplicateImages = true;
						return false;
					}
					return true;
				});

				if (haveDuplicateImages) {
					notifications.show({
						title: "Duplicate Images",
						message: "Duplicate images are not allowed.",
						color: "red",
						icon: <IconX />,
					});
				}
				return [...prev, ...newFiles];
			});
		},
		[setImages]
	);

	const handleReject = useCallback(() => {
		notifications.show({
			title: "Invalid File",
			message: "Only image files are allowed.",
			color: "red",
			icon: <IconX />,
		});
	}, []);

	const removeImage = useCallback(
		(img: File | EventDetailsType["creationMedia"][0]) => {
			const key = getImageKey(img);
			setImages(prev => {
				const newImages = prev.filter(img => getImageKey(img) !== key);
				return newImages;
			});
		},
		[setImages]
	);

	const handleThumbnailChange = useCallback(
		(
			e: React.ChangeEvent<HTMLInputElement>,
			img: File | EventDetailsType["creationMedia"][0]
		) => {
			if (e.target.checked) {
				setThumbnailImage(img);
			} else {
				setThumbnailImage(null);
			}
		},
		[setThumbnailImage]
	);

	useEffect(() => {
		if (thumbnailImage === null && images.length > 0) {
			setThumbnailImage(images[0]);
		} else if (
			thumbnailImage &&
			!images.find(
				img => getImageKey(img) === getImageKey(thumbnailImage)
			)
		) {
			setThumbnailImage(images.length > 0 ? images[0] : null);
		}
	}, [images, thumbnailImage, setThumbnailImage]);

	return (
		<>
			<Card shadow="lg" p="lg" withBorder>
				<Stack>
					<Flex align="center" gap="xs">
						<ImageIcon size={20} />
						<Title order={3}>Images</Title>
					</Flex>

					<Dropzone
						onDrop={handleDrop}
						onReject={handleReject}
						accept={{
							"image/*": [],
						}}
						multiple
					>
						<Group
							justify="center"
							gap="xl"
							mih={220}
							style={{ pointerEvents: "none" }}
						>
							<Stack align="center" gap="xs">
								<Text size="xl" inline>
									Drop Images here or click to upload images
								</Text>
								<Text size="sm" c="dimmed" inline mt={7}>
									Attach as many images as you like
								</Text>
								<Button
									mt="sm"
									style={{
										color: theme.colors.gray[7],
										backgroundColor: theme.colors.gray[1],
										height: "45px",
										width: "180px",
										borderRadius: "6px",
									}}
									styles={{
										root: {
											padding: "12px 24px",
										},
									}}
									leftSection={<ImageIcon size={16} />}
								>
									Choose Images
								</Button>
							</Stack>
						</Group>
					</Dropzone>

					{images.length > 0 && (
						<Stack mt="md">
							<Text fw={500}>
								Selected Images ({images.length})
							</Text>
							<EventImageCards
								gridProps={{
									cols: { base: 2, sm: 3, lg: 4 },
									spacing: "md",
								}}
								images={images.map(image => {
									if (image instanceof File) {
										return {
											_id: getFileKey(image),
											url: URL.createObjectURL(image),
											key: getFileKey(image),
											isThumbnail: false,
										};
									}
									return image;
								})}
								isEditMode={true}
								removeImage={removeImage}
								isThumbnail={
									thumbnailImage
										? getImageId(thumbnailImage)
										: undefined
								}
								handleThumbnailChange={handleThumbnailChange}
							/>
						</Stack>
					)}
				</Stack>
			</Card>
		</>
	);
};

export default ImageUpload;
