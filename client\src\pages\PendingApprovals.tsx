import { useCallback, useEffect, useRef, useState } from "react";
import { apiClient } from "../config/axios";
import {
	Container,
	Title,
	Divider,
	Group,
	Loader,
	Center,
	Pagination,
	Text,
	Paper,
	Stack,
	TextInput,
	Select,
	Tooltip,
	ActionIcon,
	Modal,
	Space,
} from "@mantine/core";
import UserTable from "../components/profile-approveForms/UserTable";
import { notifications } from "@mantine/notifications";
import {
	IconCheck,
	IconFilter,
	IconSearch,
	IconSortAscending,
	IconSortDescending,
	IconX,
} from "@tabler/icons-react";
import type { pendingProfilesDataType, UserCreation } from "../types";
import { useDebouncedValue } from "@mantine/hooks";
import { DEBOUNCE_TIME_IN_MS, profileStatus, sortOptions } from "../constants";
import { isAxiosError } from "axios";
import CreateUserForm from "../components/CreateUserForm";
import { useAuth } from "../contexts/AuthContext";
import ViewFeedbacks from "../components/Feedbacks/ViewFeedbacks";
import EllipsisCell from "../components/EllipsisCell";

type sortByDataType = "firstName" | "secondName" | "email" | "updatedAt";
type profileStatusDataType = "all" | "onboarding" | "pending" | "re-approved";

const PendingApprovals = () => {
	const { user } = useAuth();

	const [users, setUsers] = useState<pendingProfilesDataType[]>([]);
	const [filters, setFilters] = useState<{
		page: number;
		totalPages: number;
		limit: number;
		sortBy: sortByDataType;
		searchQuery: string;
		profileStatus: profileStatusDataType;
		sortOrder: 1 | -1;
	}>({
		page: 1,
		totalPages: 1,
		limit: 10,
		sortBy: "updatedAt",
		searchQuery: "",
		profileStatus: "all",
		sortOrder: -1,
	});
	const setPageToOne = useRef<boolean>(false);

	const [loading, setLoading] = useState(true);
	const [totalUsers, setTotalUsers] = useState(0);
	const [opened, setOpened] = useState(false);
	const [userToEdit, setUserToEdit] = useState<UserCreation | null>(null);
	const [openFeedbackModal, setOpenFeedbackModal] =
		useState<UserCreation | null>(null);

	const start = (filters.page - 1) * filters.limit + 1;
	const end = Math.min(filters.page * filters.limit, totalUsers);

	const [debouncedSearch] = useDebouncedValue(
		filters.searchQuery,
		DEBOUNCE_TIME_IN_MS
	);

	const currentUserRole: number = user?.role ?? 0;

	const fetchUsers = useCallback(
		async (currentPage: number) => {
			setLoading(true);
			try {
				const res = await apiClient.get(
					`/api/users/pending-approvals?page=${currentPage}&limit=${filters.limit}&sort=${filters.sortBy}&sortOrder=${filters.sortOrder}&search=${encodeURIComponent(debouncedSearch.trim())}&profileStatus=${filters.profileStatus}`
				);

				setPageToOne.current = false;
				setUsers(res.data.data);
				setFilters(previous => {
					return {
						...previous,
						page: currentPage,
						totalPages: res.data.totalPages,
					};
				});
				setTotalUsers(res.data.total);
			} catch (err) {
				if (isAxiosError(err)) {
					notifications.show({
						title: "Failed",
						message:
							err.response?.data?.message ||
							"Failed to fetch users",
						color: "red",
						icon: <IconX />,
					});
				} else {
					notifications.show({
						title: "Failed",
						message: "Failed to fetch users",
						color: "red",
						icon: <IconX />,
					});
				}
				console.error("Error fetching users:", err);
			} finally {
				setLoading(false);
			}
		},
		[
			filters.limit,
			filters.sortBy,
			filters.profileStatus,
			filters.sortOrder,
			debouncedSearch,
		]
	);

	useEffect(() => {
		if (setPageToOne.current && filters.page !== 1) {
			setFilters(prev => ({ ...prev, page: 1 }));
			return;
		}
		fetchUsers(filters.page);
	}, [fetchUsers, filters.page]);

	const handleUpdateUser = () => {
		fetchUsers(filters.page);
		setOpened(false);
		setUserToEdit(null);
	};

	const handleEditUser = (user: UserCreation) => {
		setUserToEdit(user);
		setOpened(true);
	};

	const handleDeleteUser = async (userId: string) => {
		try {
			const res = await apiClient.delete(`/api/users/delete/${userId}`);
			setUsers(prev => prev.filter(u => u._id !== userId));
			notifications.show({
				title: "User Deleted",
				message: res.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			fetchUsers(filters.page);
		} catch {
			notifications.show({
				title: "Failed",
				message: "Failed to delete user",
				color: "red",
				icon: <IconX />,
			});
		}
	};

	const getAllowedRoles = (): (
		| "SuperAdmin"
		| "Admin"
		| "CommunityMember"
	)[] => {
		if (currentUserRole === 1) {
			return ["SuperAdmin", "Admin", "CommunityMember"];
		}
		if (currentUserRole === 2) {
			return ["CommunityMember", "Admin"];
		}
		return [];
	};

	return (
		<Container>
			<Title order={1}>Pending Profiles</Title>
			<Divider my="md" />

			<Paper withBorder p="md" mb="md">
				<Stack gap={6}>
					<Group justify="space-between" align="center">
						<Group align="center" gap={12}>
							<TextInput
								placeholder="Search by name or email"
								leftSection={<IconSearch size={16} />}
								value={filters.searchQuery}
								// readOnly={loading}
								w={300}
								onChange={e => {
									setPageToOne.current = true;
									setFilters(prev => ({
										...prev,
										searchQuery: e.target.value,
									}));
								}}
							/>
							<Tooltip
								label={`Select field to sort by (current: ${sortOptions.find(opt => opt.value === filters.sortBy)?.label || filters.sortBy})`}
							>
								<Select
									placeholder="Sort by"
									w={180}
									value={filters.sortBy}
									disabled={loading}
									onChange={value => {
										setPageToOne.current = true;
										setFilters(prev => ({
											...prev,
											sortBy: value as sortByDataType,
											page: 1,
										}));
									}}
									data={sortOptions}
									size="sm"
									leftSection={
										<Tooltip
											label={`Toggle sort order: currently ${filters.sortOrder === 1 ? "Ascending (A→Z)" : "Descending (Z→A)"}`}
										>
											<ActionIcon
												variant="light"
												onClick={() => {
													setFilters(prev => ({
														...prev,
														sortOrder:
															prev.sortOrder === 1
																? -1
																: 1,
													}));
												}}
												disabled={loading}
											>
												{filters.sortOrder === 1 ? (
													<IconSortAscending
														size={16}
													/>
												) : (
													<IconSortDescending
														size={16}
													/>
												)}
											</ActionIcon>
										</Tooltip>
									}
								/>
							</Tooltip>

							<Tooltip
								label={`Filter by profile status (current: ${profileStatus.find(opt => opt.value === filters.profileStatus)?.label || filters.profileStatus})`}
							>
								<Select
									leftSection={<IconFilter size={14} />}
									value={filters.profileStatus}
									disabled={loading}
									onChange={val => {
										setPageToOne.current = true;
										setFilters(prev => ({
											...prev,
											profileStatus:
												val as profileStatusDataType,
											page: 1,
										}));
									}}
									data={profileStatus.filter(
										opt => opt.value !== "approved"
									)}
									size="sm"
									allowDeselect={false}
								/>
							</Tooltip>
						</Group>

						<Select
							w={180}
							value={String(filters.limit)}
							disabled={loading}
							onChange={value => {
								setFilters(prev => ({
									...prev,
									limit: Number(value),
									page: 1,
								}));
							}}
							data={[
								{ label: "5 per page", value: "5" },
								{ label: "10 per page", value: "10" },
								{ label: "20 per page", value: "20" },
								{ label: "50 per page", value: "50" },
							]}
							size="sm"
							allowDeselect={false}
						/>
					</Group>

					<Group align="center" justify="space-between">
						<Text c="gray.7" size="sm">
							Showing {totalUsers > 0 ? start : 0}-{end} of{" "}
							{totalUsers} users
						</Text>
						<Pagination
							disabled={loading}
							total={filters.totalPages}
							value={filters.page}
							onChange={page => {
								setFilters(prev => ({ ...prev, page }));
							}}
							mt="md"
							siblings={1}
							boundaries={1}
						/>
					</Group>
				</Stack>
			</Paper>

			<div>
				{loading ? (
					<Center>
						<Loader />
					</Center>
				) : (
					<>
						<UserTable
							users={users}
							onDelete={handleDeleteUser}
							onEdit={handleEditUser}
							openFeedbackModal={setOpenFeedbackModal}
						/>
						<Group justify="flex-end">
							<Pagination
								total={filters.totalPages}
								value={filters.page}
								onChange={page => {
									setFilters(prev => ({ ...prev, page }));
								}}
								mt="md"
								siblings={1}
								boundaries={1}
								disabled={loading}
							/>
						</Group>
					</>
				)}
			</div>
			{opened && (
				<Modal
					opened={opened}
					onClose={() => {
						setOpened(false);
						setUserToEdit(null);
					}}
					title={"Edit User"}
					size="lg"
					centered
					trapFocus={false}
				>
					<CreateUserForm
						allowedRoles={getAllowedRoles()}
						onUserCreated={handleUpdateUser}
						setOpened={setOpened}
						userToEdit={userToEdit}
					/>
					<Space h="md" />
				</Modal>
			)}

			{openFeedbackModal && (
				<Modal
					opened={!!openFeedbackModal}
					onClose={() => setOpenFeedbackModal(null)}
					size={"xl"}
					title={
						<EllipsisCell
							value={`${openFeedbackModal.firstName} ${openFeedbackModal.secondName}'s Feedbacks`}
							maxWidth={480}
						/>
					}
					centered
				>
					<ViewFeedbacks
						userIdFromModal={openFeedbackModal._id}
						filtersFromModal={{ status: "active" }}
						profileStatus={openFeedbackModal.profileStatus}
					/>
				</Modal>
			)}
		</Container>
	);
};

export default PendingApprovals;
