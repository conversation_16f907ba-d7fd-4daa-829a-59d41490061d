import VideoUpload from "../models/VideoUpload.js";
import { extractTextFromSpeechFromOpenAI } from "../utils/extractTextFromSpeech.js";
import { extractDataAndUpdateUser } from "./openAiDataExtraction.js";
import fs from "fs";

const transcriptionQueue = [];
let isProcessing = false;

async function processTranscription({ video, audioFilePath }) {
	try {
		const transcriptionText =
			await extractTextFromSpeechFromOpenAI(audioFilePath);

		if (!transcriptionText) {
			throw new Error("No transcription text extracted.");
		}

		await extractDataAndUpdateUser(
			transcriptionText,
			video.videoType,
			video.createdBy
		);

		await VideoUpload.findByIdAndUpdate(video._id, {
			transcription: transcriptionText,
			transcriptionStatus: "completed",
		});
	} catch (error) {
		console.error("Error processing transcription:", error);
		await VideoUpload.findByIdAndUpdate(video._id, {
			transcriptionStatus: "failed",
			errorMessage: error.message,
		});
	} finally {
		// Clean up the audio file
		if (audioFilePath && fs.existsSync(audioFilePath)) {
			fs.unlinkSync(audioFilePath);
		}
	}
}

async function startProcessing() {
	if (isProcessing || transcriptionQueue.length === 0) {
		return;
	}

	isProcessing = true;
	const data = transcriptionQueue.shift();

	await processTranscription(data);

	isProcessing = false;
	startProcessing();
}

export function addVideoToTranscriptionQueue(data) {
	transcriptionQueue.push(data);
	startProcessing();
}
