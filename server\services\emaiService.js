import nodemailer from "nodemailer";
import { APP_CONFIG } from "../config/env.js";

// Create a transporter using environment variables or default test account
let transporter;

const createTransporter = async () => {
	if (APP_CONFIG.isProduction) {
		return (transporter = nodemailer.createTransport({
			service: "Mailjet",
			auth: {
				user: APP_CONFIG.EMAIL_USER,
				pass: APP_CONFIG.EMAIL_PASS,
			},
		}));
	} else if (APP_CONFIG.isTest) {
		return (transporter = nodemailer.createTransport({
			host: APP_CONFIG.EMAIL_HOST,
			port: 1025,
			auth: {
				user: APP_CONFIG.EMAIL_USER,
				pass: APP_CONFIG.EMAIL_PASS,
			},
		}));
	} else {
		return (transporter = nodemailer.createTransport({
			host: APP_CONFIG.EMAIL_HOST || "localhost",
			port: 1025,
			secure: false,
			auth: null,
		}));
	}
};

// Initialize transporter
export const initEmailService = async () => {
	transporter = await createTransporter();
	return transporter;
};

// Send an email
export const sendEmail = async ({ to, subject, html }) => {
	if (!transporter) {
		transporter = await createTransporter();
	}

	const mailOptions = {
		from: APP_CONFIG.EMAIL_FROM || "<EMAIL>",
		to: Array.isArray(to) ? to.join(", ") : to,
		subject,
		html,
	};

	try {
		const info = await transporter.sendMail(mailOptions);
		console.log("Message sent: %s", info.messageId);
		// If using Ethereal, show the preview URL
		if (info.preview) {
			console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
		}
		return info;
	} catch (error) {
		console.error("Error sending email:", error);
		throw error; // Re-throw to allow calling code to handle it
	}
};

export default {
	initEmailService,
	sendEmail,
};
