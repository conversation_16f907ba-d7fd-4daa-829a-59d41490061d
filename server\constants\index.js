export const rolesValues = {
	SuperAdmin: 1,
	Admin: 2,
	CommunityMember: 3,
};

export const rolesValuesNumToStr = {
	1: "SuperAdmin",
	2: "Admin",
	3: "CommunityMember",
};

export const AllProfileStatus = [
	"pending",
	"onboarding",
	"re-approved",
	"approved",
	"changes_requested",
];

export const ProfileStatus = {
	Pending: "pending",
	Onboarding: "onboarding",
	Approved: "approved",
	ReApproved: "re-approved",
	ChangesRequested: "changes_requested",
};

export const videoTypeMapping = {
	EarlyLife: "EarlyLife",
	ProfessionalLife: "ProfessionalLife",
	CurrentLife: "CurrentLife",
};

export const videoTypeStrToNum = {
	EarlyLife: 1,
	ProfessionalLife: 2,
	CurrentLife: 3,
};

export const videoTypeNumToStr = {
	1: "EarlyLife",
	2: "ProfessionalLife",
	3: "CurrentLife",
};

export const videoTypeWithSpacing = {
	EarlyLife: "Early Life",
	ProfessionalLife: "Professional Life",
	CurrentLife: "Current Life",
};

export const USER_FIELD_MAP = {
	[videoTypeNumToStr[1]]: {
		onboardingLifeData: "earlyLifeData",
		onboardingUpdatedLifeData: "updatedEarlyLifeData",
	},
	[videoTypeNumToStr[2]]: {
		onboardingLifeData: "professionalLifeData",
		onboardingUpdatedLifeData: "updatedProfessionalLifeData",
	},
	[videoTypeNumToStr[3]]: {
		onboardingLifeData: "currentLifeData",
		onboardingUpdatedLifeData: "updatedCurrentLifeData",
	},
};

export const ONBOARDING_STEP = {
	BASIC_DETAILS: 1,
	EARLY_LIFE_VIDEO: 2,
	EARLY_LIFE_FORM: 3,
	PROFESSIONAL_LIFE_VIDEO: 4,
	PROFESSIONAL_LIFE_FORM: 5,
	CURRENT_LIFE_VIDEO: 6,
	CURRENT_LIFE_FORM: 7,
	FINAL_SUBMIT: 8,
	WAIT_FOR_APPROVAL: 9,
	COMPLETED: 10,
};

export const MAX_VIDEO_PROCESS_RETRY_COUNT = 5;
export const PUBLIC_NAME = "Supermorpheus";
export const PUBLIC_EMAIL = "<EMAIL>";
export const PUBLIC_CONTACT_PERSON_NAME = "Yatin";

export const EVENT_STATUS = {
	Published: "published",
	Unpublished: "unpublished",
};

export const EVENT_ATTENDANCE_STATUS = {
	Attending: "attending",
	NotAttending: "not_attending",
};

export const EVENT_MEDIA_SOURCE = {
	Creation: "creation",
	Community: "community",
};

export const EVENT_MEDIA_APPROVED_STATUS = {
	Pending: "pending",
	Approved: "approved",
};
