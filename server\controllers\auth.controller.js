import jwt from "jsonwebtoken";
import { APP_CONFIG } from "../config/env.js";
import User from "../models/User.js";
import { checkIfPasswordIsValid } from "../utils/encryption.js";

const generateAccessToken = user => {
	const payloadData = {
		email: user.email,
		role: user.role,
		userId: user._id,
	};
	const token = jwt.sign(payloadData, APP_CONFIG.ACCESS_TOKEN_SECRET, {
		expiresIn: APP_CONFIG.ACCESS_TOKEN_EXPIRE_TIME,
	});
	return token;
};

export const loginUser = async (req, res) => {
	const { email, password } = req.body;

	const user = await User.findOne({ email }).select("+password");
	if (!user) {
		return res.status(404).json({
			message: "User not found",
		});
	}

	const isMatch = checkIfPasswordIsValid(password, user.password);
	if (!isMatch) {
		return res.status(400).json({
			error: "Password is not valid",
		});
	}

	const token = generateAccessToken(user);
	return res.status(200).json({
		token,
		user: {
			id: user._id,
			email: user.email,
			role: user.role,
		},
	});
};
