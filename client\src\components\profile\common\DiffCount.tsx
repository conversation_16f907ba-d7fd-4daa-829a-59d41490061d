import { Badge, Group } from "@mantine/core";

const DiffCount = ({ totalChanges }: { totalChanges: number }) => {
	return (
		<Group mb={16}>
			<Badge
				variant="light"
				color="gray"
				size="lg"
				style={{
					paddingBlock: "14px",
					paddingInline: "12px",
					borderRadius: "8px",
					textTransform: "capitalize",
				}}
			>
				Total Changes: {totalChanges}
			</Badge>
		</Group>
	);
};

export default DiffCount;
