import {
	Stack,
	Group,
	Text,
	Divider,
	ThemeIcon,
	Flex,
	Button,
	Paper,
} from "@mantine/core";
import {
	IconMapPin,
	IconBuildingSkyscraper,
	IconPlaneDeparture,
	IconInfoCircle,
	IconTags,
} from "@tabler/icons-react";
import React, { useEffect, useState } from "react";
import apiClient from "../../config/axios";
import type { CurrentLifeDataType, DiffChange } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import FullScreenLoader from "../FullScreenLoader";
import { getChangedValue, getDiff, getDiffStyle } from "../../utils/deep-diff";
import LifeTags from "./common/LifeTags";
import RenderDiff from "./common/RenderDiff";
import DiffList from "./common/DiffList";
import DiffCount from "./common/DiffCount";
import { Edit } from "lucide-react";

interface CurrentLifePreviewProps {
	showEdit?: boolean;
	setEditing?: (value: boolean) => void;
	lifeData?: CurrentLifeDataType;
	updatedLifeData?: CurrentLifeDataType;
	userId?: string;
}

const CurrentLifePreview: React.FC<CurrentLifePreviewProps> = ({
	showEdit,
	setEditing,
	lifeData,
	updatedLifeData,
	userId,
}) => {
	const [currentLife, setCurrentLife] = useState<CurrentLifeDataType | null>(
		null
	);
	const [diffChanges, setDiffChanges] = useState<DiffChange[] | null>(null);
	const [totalChangesCount, setSetTotalChangesCount] = useState<number>(0);

	const fetchData = async () => {
		try {
			const response = await apiClient.get<CurrentLifeDataType>(
				"/api/lifeData/currentLife"
			);
			setCurrentLife(response.data);
		} catch (err) {
			console.error("Error fetching current life data:", err);
		}
	};

	useEffect(() => {
		if (!userId && !lifeData) {
			fetchData();
		} else if (lifeData) {
			setCurrentLife(lifeData);
		}
	}, [lifeData, userId]);

	useEffect(() => {
		if (lifeData && updatedLifeData) {
			const deepDiff = getDiff({
				originalData: lifeData,
				updatedData: updatedLifeData,
				ignoredKeys: ["videoUrl"],
			});
			if (deepDiff && deepDiff.changes.length > 0) {
				setDiffChanges(deepDiff.changes);
				setSetTotalChangesCount(deepDiff.total);
			} else {
				setDiffChanges(null);
			}
		}
	}, [lifeData, updatedLifeData]);
	if (!currentLife) return <FullScreenLoader />;

	return (
		<Paper p="md">
			{showEdit && (
				<Flex justify="space-between">
					{totalChangesCount > 0 && (
						<DiffCount totalChanges={totalChangesCount} />
					)}
					<Button
						onClick={() => setEditing?.(true)}
						mb={16}
						ml={"auto"}
						leftSection={<Edit size={16} />}
					>
						Edit
					</Button>
				</Flex>
			)}
			<VideoPreviewAndUpload
				editing={true}
				videoPreviewUrl={currentLife.videoUrl}
				videoType="CurrentLife"
			/>

			<Stack gap="lg">
				<Flex gap="xs" align="flex-start">
					<Group>
						<ThemeIcon variant="light" color="blue">
							<IconInfoCircle size={16} />
						</ThemeIcon>
					</Group>
					<Stack gap={0} justify="flex-start">
						<Text c="dimmed" size="xs">
							Summary:
						</Text>
						{(() => {
							const diff = getChangedValue(
								["currentLifeSummary"],
								diffChanges
							);
							return diff ? (
								<RenderDiff elementType="text" {...diff} />
							) : (
								<RenderDiff
									elementType="text"
									oldValue={currentLife.currentLifeSummary}
									newValue={null}
									type={"unchanged"}
									size="sm"
								/>
							);
						})()}
					</Stack>
				</Flex>

				<LifeTags
					tags={currentLife.currentCities}
					diffChanges={diffChanges}
					diffPathPrefix="currentCities"
					icon={
						<ThemeIcon variant="light" color="teal" mt={2}>
							<IconMapPin size={16} />
						</ThemeIcon>
					}
					label="Current Cities"
					labelProps={{
						size: "sm",
						fw: 500,
					}}
				/>

				<DiffList
					label="Organizations"
					icon={
						<ThemeIcon variant="light" color="teal">
							<IconBuildingSkyscraper size={16} />
						</ThemeIcon>
					}
					data={currentLife.currentOrganizations}
					diffChanges={diffChanges ?? null}
					pathKey="currentOrganizations"
					getStyle={getDiffStyle}
					fields={[
						{ key: "name", props: { c: "dimmed", size: "xs" } },
						{ key: "role", props: { size: "sm" } },
					]}
				/>

				<Divider
					label="Frequent Travel Cities"
					labelPosition="center"
				/>

				<Flex align="start" gap="sm">
					<LifeTags
						tags={currentLife.frequentTravelCities}
						diffChanges={diffChanges}
						diffPathPrefix="frequentTravelCities"
						icon={
							<ThemeIcon variant="light" color="green" mt={2}>
								<IconPlaneDeparture size={16} />
							</ThemeIcon>
						}
					/>
				</Flex>

				<LifeTags
					tags={currentLife.currentLifeTags}
					diffChanges={diffChanges}
					diffPathPrefix="currentLifeTags"
					icon={
						<ThemeIcon variant="light" color="yellow" mt={2}>
							<IconTags size={16} />
						</ThemeIcon>
					}
					dividerLabelProps={{
						label: "Tags",
						labelPosition: "center",
					}}
				/>
			</Stack>
		</Paper>
	);
};

export default CurrentLifePreview;
