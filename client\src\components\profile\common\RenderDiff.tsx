import React from "react";
import { Badge, Text, Group } from "@mantine/core";

interface RenderDiffProps {
	oldValue: string | null;
	newValue: string | null;
	type: "added" | "deleted" | "edited" | "unchanged";
	elementType: "text" | "tag";
	size?: "xs" | "sm" | "md" | "lg" | "xl";
	c?: string;
}

const RenderDiff: React.FC<RenderDiffProps> = ({
	oldValue,
	newValue,
	type,
	elementType,
	size = "md",
	...props
}) => {
	const renderTextDiff = () => {
		switch (type) {
			case "edited":
				return (
					<Group gap="xs" wrap="wrap">
						{oldValue ? (
							<Text
								component="span"
								td="line-through"
								c={"red.6"}
								size={size}
							>
								{oldValue}
							</Text>
						) : (
							<Text component="span" c="red.6" size={size}>
								NA
							</Text>
						)}
						{newValue && (
							<Text component="span" c="green.8" size={size}>
								{newValue}
							</Text>
						)}
					</Group>
				);
			case "added":
				return (
					<Text c="green.8" size={size}>
						{newValue}
					</Text>
				);
			case "deleted":
				return (
					<Text c="red.6" td="line-through" size={size}>
						{oldValue}
					</Text>
				);
			case "unchanged":
				return (
					<Text size={size} {...props}>
						{oldValue || "NA"}
					</Text>
				);
			default:
				return null;
		}
	};

	const renderTagDiff = () => {
		switch (type) {
			case "edited":
				return (
					<Group gap="xs" wrap="nowrap">
						<Badge
							color="red"
							variant="light"
							style={{
								paddingBlock: "12px",
								paddingInline: "12px",
								textTransform: "capitalize",
							}}
							fz={"xs"}
						>
							{oldValue}
						</Badge>
						<Badge
							color="green"
							variant="light"
							style={{
								paddingBlock: "12px",
								paddingInline: "12px",
								textTransform: "capitalize",
							}}
							fz={"xs"}
						>
							{newValue}
						</Badge>
					</Group>
				);
			case "added":
				return (
					<Badge
						color="green"
						variant="light"
						style={{
							paddingBlock: "12px",
							paddingInline: "12px",
							textTransform: "capitalize",
						}}
						fz={"xs"}
					>
						{newValue}
					</Badge>
				);
			case "deleted":
				return (
					<Badge
						color="red"
						variant="light"
						style={{
							paddingBlock: "12px",
							paddingInline: "12px",
							textTransform: "capitalize",
						}}
						fz={"xs"}
					>
						{oldValue}
					</Badge>
				);
			case "unchanged":
				return (
					<Badge
						variant="light"
						style={{
							paddingBlock: "12px",
							paddingInline: "12px",
							textTransform: "capitalize",
						}}
						fz={"xs"}
					>
						{oldValue}
					</Badge>
				);
			default:
				return null;
		}
	};

	return elementType === "text" ? renderTextDiff() : renderTagDiff();
};

export default RenderDiff;
