import {
	Accordion,
	ActionIcon,
	Chip,
	Divider,
	Flex,
	Group,
	Select,
	Pagination,
	Paper,
	Stack,
	Text,
	TextInput,
	Title,
	Tooltip,
	Image,
	Button,
} from "@mantine/core";
import { useDebouncedValue } from "@mantine/hooks";
import { IconArrow<PERSON>eft, IconFilter, IconSearch } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import apiClient from "../../config/axios";
import type { FeedbackDataType, profileStatusDataType } from "../../types";
import { notifications } from "@mantine/notifications";
import empty from "../../assets/empty.svg";
import FullScreenLoader from "../FullScreenLoader";
import { FeedbackCard } from "./FeedbackCard";
import { ProfileStatusMapping } from "../../constants";
import EllipsisCell from "../EllipsisCell";

type propsTypes = {
	userIdFromModal?: string;
	filtersFromModal?: {
		status: string;
	};
	profileStatus?: profileStatusDataType;
};

const ViewFeedbacks = (props: propsTypes) => {
	const { userIdFromModal, filtersFromModal } = props;
	const navigate = useNavigate();
	const userId = useParams().userId || userIdFromModal;
	const [userDetails, setUserDetails] = useState<{
		_id: string;
		firstName: string;
		secondName: string;
		email: string;
		image: string;
	} | null>(null);

	const [feedbacks, setFeedbacks] = useState<FeedbackDataType[]>([]);
	const [loading, setLoading] = useState<boolean>(true);

	const [totalFeedbacks, setTotalFeedbacks] = useState<number>(0);
	const [filters, setFilters] = useState<{
		activePage: number;
		totalPages: number;
		limit: number;
		status: string;
		search: string;
		view: string;
		categories: string[];
	}>({
		activePage: 1,
		totalPages: 1,
		status: filtersFromModal?.status || "active",
		search: "",
		limit: 10,
		view: "all",
		categories: [],
	});

	const setPageToOne = useRef<boolean>(false);

	const [isUserResendToOnboarding, setIsUserResendToOnboarding] =
		useState(false);
	const [debouncedSearch] = useDebouncedValue(filters.search, 500);

	const fetchFeedbacks = useCallback(async () => {
		try {
			setLoading(true);
			const response = await apiClient.get(
				`/api/feedbacks/get-user-feedbacks/${userId}`,
				{
					params: {
						page: filters.activePage,
						limit: filters.limit,
						status: filters.status,
						search: debouncedSearch,
						view: filters.view,
						categories: filters.categories.join(","),
					},
				}
			);

			setPageToOne.current = false;

			setFeedbacks(response.data.data);
			setUserDetails(response.data.recipient);
			setFilters(prev => ({
				...prev,
				activePage: response.data.page,
				totalPages: response.data.totalPages,
			}));
			setIsUserResendToOnboarding(
				response.data.resendToOnboarding ? true : false
			);
			setTotalFeedbacks(response.data.total);
		} catch (error) {
			console.error(error);
			notifications.show({
				title: "Failed",
				message: isAxiosError(error)
					? error.response?.data?.message ||
						"Failed to fetch feedbacks"
					: "Failed to fetch feedbacks",
				color: "red",
			});
		} finally {
			setLoading(false);
		}
	}, [
		debouncedSearch,
		filters.activePage,
		filters.categories,
		filters.limit,
		filters.status,
		filters.view,
		userId,
	]);

	useEffect(() => {
		if (setPageToOne.current && filters.activePage !== 1) {
			setFilters(prev => ({ ...prev, activePage: 1 }));
			return;
		}
		fetchFeedbacks();
	}, [fetchFeedbacks, debouncedSearch, filters.activePage]);

	const { start, end } = useMemo(() => {
		if (!totalFeedbacks) return { start: 0, end: 0 };
		const limit = 10;
		const start = (filters.activePage - 1) * limit + 1;
		const end = Math.min(filters.activePage * limit, totalFeedbacks);
		return { start, end };
	}, [filters.activePage, totalFeedbacks]);

	return (
		<>
			<Stack>
				{!userIdFromModal && (
					<>
						<Flex gap={10} align="center">
							<Tooltip label="Back" withArrow>
								<ActionIcon
									variant="subtle"
									size="lg"
									onClick={() => window.history.back()}
								>
									<IconArrowLeft size={20} />
								</ActionIcon>
							</Tooltip>
							<Flex direction="column">
								<Title order={2}>
									<EllipsisCell
										value={`${userDetails?.firstName} ${userDetails?.secondName}'s Feedbacks`}
										maxWidth={480}
									/>
								</Title>
							</Flex>
						</Flex>
						<Divider />
					</>
				)}
				{userIdFromModal && (
					<Flex justify={"flex-end"} gap={8}>
						{props.profileStatus ===
							ProfileStatusMapping.Approved ||
							(props.profileStatus ===
								ProfileStatusMapping.ReApproved && (
								<Button
									variant="outline"
									onClick={() => {
										navigate(
											`/search/${userId}/Basic Details`
										);
									}}
								>
									View Profile
								</Button>
							))}
						{(props.profileStatus !== "approved" ||
							isUserResendToOnboarding) && (
							<Button
								variant="outline"
								color="green"
								onClick={() => {
									navigate(
										`/pending-profiles/user/${userId}/Basic Details`
									);
								}}
							>
								Pending Review
							</Button>
						)}
					</Flex>
				)}

				<Paper withBorder p="lg">
					<Flex justify="space-between" mb="lg" gap={20}>
						<TextInput
							placeholder="Search by admin name, email or feedback message"
							leftSection={<IconSearch size={16} />}
							value={filters.search}
							onChange={e => {
								setPageToOne.current = true;
								setFilters(prev => ({
									...prev,
									search: e.target.value,
								}));
							}}
							w={"80%"}
						/>
						<Select
							leftSection={<IconFilter size={14} />}
							w={200}
							value={filters.status}
							onChange={value => {
								setFilters(prev => ({
									...prev,
									status: value ?? "active",
									activePage: 1,
								}));
							}}
							data={[
								{ label: "Active", value: "active" },
								{ label: "Archived", value: "archived" },
							]}
							size="sm"
							allowDeselect={false}
						/>
						<Select
							leftSection={<IconFilter size={14} />}
							w={200}
							value={filters.view}
							onChange={value => {
								setFilters(prev => ({
									...prev,
									view: value ?? "all",
									activePage: 1,
								}));
							}}
							data={[
								{ label: "All", value: "all" },
								{ label: "Read", value: "read" },
								{ label: "Unread", value: "unread" },
							]}
							size="sm"
							allowDeselect={false}
						/>
					</Flex>
					<Flex justify="space-between" align={"center"} gap={10}>
						<Chip.Group
							multiple
							value={filters.categories}
							onChange={e => {
								setFilters(prev => ({
									...prev,
									categories: e,
									activePage: 1,
								}));
							}}
						>
							<Group justify="flex-start" align="center" gap={8}>
								<Chip value="basicDetails">Basic Details</Chip>
								<Chip value="earlyLife">Early Life</Chip>
								<Chip value="professionalLife">
									Professional Life
								</Chip>
								<Chip value="currentLife">Current Life</Chip>
							</Group>
						</Chip.Group>
						<Select
							w={150}
							value={String(filters.limit)}
							onChange={value => {
								setFilters(prev => ({
									...prev,
									activePage: 1,
									limit: Number(value),
								}));
							}}
							data={[
								{ label: "5 per page", value: "5" },
								{ label: "10 per page", value: "10" },
								{ label: "20 per page", value: "20" },
							]}
							size="sm"
							allowDeselect={false}
						/>
					</Flex>
					<Flex
						justify="space-between"
						align="center"
						gap={2}
						mt={20}
					>
						<Text c="gray.7" size="sm">
							Showing {start}-{end} of {totalFeedbacks} feedbacks
						</Text>
						<Pagination
							value={filters.activePage}
							onChange={page => {
								setFilters(prev => ({
									...prev,
									activePage: page,
								}));
							}}
							total={filters.totalPages}
							siblings={1}
							boundaries={1}
						/>
					</Flex>
				</Paper>
				{loading && <FullScreenLoader />}
				<Paper withBorder p="lg">
					{feedbacks && feedbacks.length > 0 ? (
						<Accordion
							multiple
							chevronPosition="right"
							variant="filled"
							style={{
								display: "flex",
								flexDirection: "column",
								gap: 12,
							}}
						>
							{(feedbacks ?? []).map(feedback => (
								<FeedbackCard
									key={feedback._id}
									feedback={feedback}
								/>
							))}
						</Accordion>
					) : (
						<Flex
							justify="center"
							align="center"
							direction="column"
						>
							<Image src={empty} alt="empty" w={140} />
							<Text c={"gray"} fw={500}>
								No feedbacks found.
							</Text>
						</Flex>
					)}
				</Paper>
			</Stack>
		</>
	);
};

export default ViewFeedbacks;
