import fs from "fs";
import { APP_CONFIG } from "../config/env.js";
import OpenAI from "openai";

const openai = new OpenAI({
	apiKey: APP_CONFIG.OPENAI_API_KEY,
});

export const extractTextFromSpeechFromOpenAI = async audioFilePath => {
	if (!fs.existsSync(audioFilePath)) {
		throw new Error(`Audio file not found: ${audioFilePath}`);
	}

	console.log("Sending audio to OpenAI API...");
	const transcription = await openai.audio.transcriptions.create({
		file: fs.createReadStream(audioFilePath),
		model: "gpt-4o-mini-transcribe",
		prompt: `Please produce a complete, fluent English transcript of the audio.
              - If the audio contains both Hindi and English, transcribe the English parts as they are and translate all Hindi parts into English, keeping them in the correct order.
              - If the audio is only in Hindi, translate it fully into English.
              - Preserve meaning, tone, and context.
              - If any words are unclear, infer and fill gaps using surrounding context so the transcript remains coherent.`,
	});
	console.log(
		"Token used for Extract Text From Speech:",
		transcription.usage.total_tokens
	);

	console.log("Transcription response:", transcription.text);
	return transcription.text;
};
