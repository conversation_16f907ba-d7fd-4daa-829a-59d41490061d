import { But<PERSON>, Container, Group, Title } from "@mantine/core";
import { useNavigate } from "react-router-dom";
import { ListPlus } from "lucide-react";
import EventsList from "../components/events/event-list/EventsList";
import { useAuth } from "../contexts/AuthContext";
import { roleValues } from "../constants";

const Events = () => {
	const navigate = useNavigate();
	const { user } = useAuth();
	return (
		<Container p={0}>
			<Group justify="space-between">
				<Title order={1}>Events</Title>
				{user?.role !== roleValues.CommunityMember && (
					<Button
						leftSection={<ListPlus size={16} />}
						onClick={() => navigate("/events/create-event")}
					>
						Create Event
					</Button>
				)}
			</Group>

			<EventsList />
		</Container>
	);
};

export default Events;
