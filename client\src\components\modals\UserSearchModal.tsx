import {
	Avatar,
	Box,
	Group,
	TextInput,
	UnstyledButton,
	Text,
	Center,
	Stack,
	ScrollArea,
	ThemeIcon,
	Flex,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import {
	IconMail,
	IconSearch,
	IconUserOff,
	IconUserShield,
} from "@tabler/icons-react";
import type { UserSearchResult } from "../../types";
import { randomId, useDebouncedValue } from "@mantine/hooks";
import { useEffect, useRef, useState } from "react";
import apiClient from "../../config/axios";
import { useNavigate } from "react-router-dom";
import { roleLabels, rolesLabelMap } from "../../constants";
import { resolveImageUrl } from "../../utils/imageUrl";
import FullScreenLoader from "../FullScreenLoader";
import EllipsisCell from "../EllipsisCell";

interface UserSearchModalContentProps {
	modalId: string;
}

interface UserItemProps {
	user: UserSearchResult;
	modalId: string;
}

export function UserItem({ user, modalId }: UserItemProps) {
	const navigate = useNavigate();

	const handleUserClick = () => {
		modals.close(modalId);
		navigate(`/search/${user._id}`);
	};

	return (
		<UnstyledButton
			w="95%"
			p="lg"
			style={{
				borderRadius: "var(--mantine-radius-md)",
				transition: "background-color 150ms ease",
				cursor: "pointer",
			}}
			onMouseEnter={e => {
				e.currentTarget.style.backgroundColor =
					"var(--mantine-color-gray-3)";
			}}
			onMouseLeave={e => {
				e.currentTarget.style.backgroundColor = "";
			}}
			onClick={handleUserClick}
		>
			<Group align="flex-start">
				<Avatar
					src={resolveImageUrl(user.image) || undefined}
					radius="xl"
					size="lg"
				>
					{!user.image && user.firstName[0]}
				</Avatar>

				<Stack gap={2} justify="center" style={{ flex: 1 }}>
					<Flex align={"center"} justify={"space-between"}>
						<EllipsisCell
							value={`${user.firstName} ${user.middleName} ${user.secondName}`}
							maxWidth={480}
						/>
					</Flex>

					<Group gap="xs">
						<ThemeIcon variant="light" size="sm" color="gray">
							<IconMail size={14} />
						</ThemeIcon>
						<Text size="xs" c="dimmed">
							<EllipsisCell value={user.email} maxWidth={380} />
						</Text>
					</Group>

					<Group gap="xs">
						<ThemeIcon variant="light" size="sm" color="gray">
							<IconUserShield size={14} />
						</ThemeIcon>
						<Text size="xs" c="gray">
							{
								rolesLabelMap[
									roleLabels[
										user.role as keyof typeof roleLabels
									]
								]
							}
						</Text>
					</Group>
				</Stack>
			</Group>
		</UnstyledButton>
	);
}

export function UserSearchModalContent({
	modalId,
}: UserSearchModalContentProps) {
	const [searchQuery, setSearchQuery] = useState("");
	const [debouncedQuery] = useDebouncedValue(searchQuery, 300);
	const [results, setResults] = useState<UserSearchResult[]>([]);
	const [loading, setLoading] = useState(false);
	const inputRef = useRef<HTMLInputElement>(null);

	useEffect(() => {
		const t = setTimeout(() => {
			inputRef.current?.focus();
		}, 10);

		return () => clearTimeout(t);
	}, []);

	useEffect(() => {
		const searchUsers = async () => {
			if (debouncedQuery.trim() === "") {
				setResults([]);
				setLoading(false);
				return;
			}

			setLoading(true);

			try {
				const response = await apiClient.get<UserSearchResult[]>(
					`/api/users/search`,
					{
						params: { query: debouncedQuery },
					}
				);
				setResults(
					response.data.map((item: Partial<UserSearchResult>) => ({
						_id: item._id || "",
						firstName: item.firstName || "",
						middleName: item.middleName || "",
						secondName: item.secondName || "",
						image: item.image || "",
						email: item.email || "",
						role: item.role || 3,
					}))
				);
			} catch (err) {
				console.error("Error fetching search results:", err);
			} finally {
				setLoading(false);
			}
		};

		searchUsers();
	}, [debouncedQuery]);

	const renderContent = () => {
		if (loading) {
			return <FullScreenLoader />;
		}

		if (debouncedQuery.trim() !== "" && results.length === 0) {
			return (
				<Center h={200} style={{ flexDirection: "column", gap: 8 }}>
					<IconUserOff size={40} stroke={1.5} color="gray" />
					<Text c="dimmed" fw={500}>
						No users found
					</Text>
					<Text size="sm" c="dimmed">
						Try a different search keyword.
					</Text>
				</Center>
			);
		}

		if (results.length > 0) {
			return (
				<Stack gap="xs" mt="md">
					{results.map(user => (
						<UserItem
							key={user._id}
							user={user}
							modalId={modalId}
						/>
					))}
				</Stack>
			);
		}

		return (
			<Center h={200} style={{ flexDirection: "column", gap: 8 }}>
				<IconSearch size={40} stroke={1.5} color="gray" />
				<Text c="dimmed" fw={500}>
					Start typing to search for users
				</Text>
			</Center>
		);
	};

	return (
		<Box>
			<TextInput
				ref={inputRef}
				placeholder="Search by name, email or tags"
				leftSection={<IconSearch size={16} />}
				value={searchQuery}
				onChange={event => setSearchQuery(event.currentTarget.value)}
			/>
			<ScrollArea h={250} mt="md">
				{renderContent()}
			</ScrollArea>
		</Box>
	);
}

// eslint-disable-next-line react-refresh/only-export-components
export function openUserSearchModal() {
	const modalId = randomId();

	modals.open({
		modalId,
		title: "Search Users",
		centered: true,
		size: "xl",
		children: <UserSearchModalContent modalId={modalId} />,
	});
}
