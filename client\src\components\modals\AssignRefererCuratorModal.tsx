import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	Text,
	Group,
	Badge,
	Flex,
	ThemeIcon,
	UnstyledButton,
	useCombobox,
	Title,
	Grid,
	Card,
	TextInput,
	Combobox,
	Loader,
} from "@mantine/core";
import { useState, useCallback, useEffect } from "react";
import apiClient from "../../config/axios";
import {
	IconUser,
	IconMail,
	IconCircleCheck,
	IconX,
	IconCheck,
} from "@tabler/icons-react";
import { useDebouncedCallback } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import type {
	profileStatusDataType,
	UserRefererCrutorSearchResult,
} from "../../types";
import { isAxiosError } from "axios";
import { useAuth } from "../../contexts/AuthContext";
interface Props {
	userId: string | undefined;
	opened: boolean;
	onClose: () => void;
	onConfirm: () => void;
	isReApproved: boolean;
}

interface UserSearchState {
	searchQuery: string;
	selectedId: string | null;
	selectedLabel: string;
	data: UserRefererCrutorSearchResult[];
	loading: boolean;
	error: string | null;
}

export default function AssignRefererCuratorModal({
	userId,
	opened,
	onClose,
	onConfirm,
	isReApproved,
}: Props) {
	const { fetchUser } = useAuth();
	const [refererState, setRefererState] = useState<UserSearchState>({
		searchQuery: "",
		selectedId: null,
		selectedLabel: "",
		data: [],
		loading: false,
		error: null,
	});

	const [curatorState, setCuratorState] = useState<UserSearchState>({
		searchQuery: "",
		selectedId: null,
		selectedLabel: "",
		data: [],
		loading: false,
		error: null,
	});

	const [isSubmitting, setIsSubmitting] = useState(false);

	const fetchRefereCrutor = useCallback(async () => {
		try {
			const response = await apiClient.get(
				`/api/users/referer-curator-user/${userId}`
			);
			setRefererState(() => {
				return {
					data: [],
					error: null,
					loading: false,
					selectedId: response.data.referer._id,
					selectedLabel: response.data.referer.email,
					searchQuery: response.data.referer.email,
				};
			});
			setCuratorState(() => {
				return {
					data: [],
					error: null,
					loading: false,
					selectedId: response.data.curator._id,
					selectedLabel: response.data.curator.email,
					searchQuery: response.data.curator.email,
				};
			});
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Get User",
					message: "Failed to fetch User",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Get User",
					message: "Failed to fetch User",
					color: "red",
				});
			}
		}
	}, [userId]);

	useEffect(() => {
		if (isReApproved) {
			fetchRefereCrutor();
		}
	}, [fetchRefereCrutor, isReApproved]);

	const fetchUsers = useCallback(
		async (
			query: string,
			setState: React.Dispatch<React.SetStateAction<UserSearchState>>
		) => {
			if (!query.trim()) {
				setState(prev => ({ ...prev, data: [], error: null }));
				return;
			}
			setState(prev => ({ ...prev, loading: true, error: null }));
			try {
				const response = await apiClient.get(
					`/api/users/search-referer-curator?query=${encodeURIComponent(query.trim())}`
				);
				setState(prev => ({
					...prev,
					data: response.data || [],
					loading: false,
				}));
			} catch (error) {
				console.error("User search error:", error);
				const errorMessage =
					"Failed to search for users. Please try again.";
				setState(prev => ({
					...prev,
					data: [],
					loading: false,
					error: errorMessage,
				}));
				notifications.show({
					title: "Search Error",
					message: errorMessage,
					color: "red",
				});
			}
		},
		[]
	);

	const debouncedFetchReferers = useDebouncedCallback(
		(query: string) => fetchUsers(query, setRefererState),
		300
	);

	const debouncedFetchCurators = useDebouncedCallback(
		(query: string) => fetchUsers(query, setCuratorState),
		300
	);

	const handleRefererSelect = (user: UserRefererCrutorSearchResult) => {
		setRefererState(prev => ({
			...prev,
			selectedId: user._id,
			selectedLabel: user.email,
			searchQuery: user.email,
		}));
		refererCombobox.closeDropdown();
	};

	const handleCuratorSelect = (user: UserRefererCrutorSearchResult) => {
		setCuratorState(prev => ({
			...prev,
			selectedId: user._id,
			selectedLabel: user.email,
			searchQuery: user.email,
		}));
		curatorCombobox.closeDropdown();
	};

	const changeProfileStatus = async (status: profileStatusDataType) => {
		try {
			if (!refererState.selectedId || !curatorState.selectedId) {
				notifications.show({
					title: "Error",
					message: "Please select referer and curator",
					color: "red",
					icon: <IconX />,
				});
				return;
			}
			setIsSubmitting(true);
			const response = await apiClient.put(
				`/api/users/change-profile-status/${userId}`,
				{
					status,
					refererId: refererState.selectedId,
					curatorId: curatorState.selectedId,
				}
			);
			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			fetchUser();
			onConfirm();
			handleClose();
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Error",
					message:
						error.response?.data?.message ||
						`Failed to ${status} user}`,
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Error",
					message: `Failed to ${status} user}`,
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = useCallback(() => {
		setRefererState({
			searchQuery: "",
			selectedId: null,
			selectedLabel: "",
			data: [],
			loading: false,
			error: null,
		});
		setCuratorState({
			searchQuery: "",
			selectedId: null,
			selectedLabel: "",
			data: [],
			loading: false,
			error: null,
		});
		setIsSubmitting(false);
		onClose();
	}, [onClose]);

	const isFormValid = refererState.selectedId && curatorState.selectedId;

	const refererCombobox = useCombobox();
	const curatorCombobox = useCombobox();

	return (
		<Modal
			opened={opened}
			onClose={handleClose}
			centered
			size="xl"
			closeOnClickOutside={!isSubmitting}
			closeOnEscape={!isSubmitting}
			styles={{ content: { borderRadius: "16px" } }}
			title={
				<Group>
					<div className="w-10 h-10 bg-[var(--mantine-color-blue-1)] rounded-xl flex items-center justify-center">
						<IconUser size={20} />
					</div>
					<Flex direction="column" justify="center">
						<Title order={4}>Approve User</Title>
						<Text>
							Select referer and curator for user approval
						</Text>
					</Flex>
				</Group>
			}
			trapFocus={false}
		>
			<>
				<Grid gutter="xl" mt={"md"}>
					<Grid.Col span={{ base: 12, md: 6 }}>
						<Card
							withBorder
							radius="md"
							p="lg"
							style={{
								borderColor: "var(--mantine-color-blue-3)",
								background: "var(--mantine-color-blue-1)",
							}}
						>
							<Group justify="space-between">
								<Group>
									<div
										style={{
											width: 32,
											height: 32,
											backgroundColor:
												"var(--mantine-color-blue-6)",
											borderRadius: 8,
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
										}}
									>
										<Text c="white" size="sm" fw={600}>
											R
										</Text>
									</div>
									<Text size="lg" fw={600} c="gray.9">
										Referer
									</Text>
								</Group>
								{refererState.selectedId && (
									<Badge
										color="green"
										variant="light"
										leftSection={
											<IconCircleCheck size={14} />
										}
									>
										Selected
									</Badge>
								)}
							</Group>

							<Text size="sm" fw={500} c="gray.7" mt="md">
								Email Address
							</Text>

							<Combobox store={refererCombobox}>
								<Combobox.Target>
									<TextInput
										value={refererState.searchQuery}
										onChange={e => {
											const query = e.target.value;

											setRefererState(prev => ({
												...prev,
												searchQuery: query,
												selectedId: null,
											}));
											debouncedFetchReferers(query);
											if (query.trim()) {
												refererCombobox.openDropdown();
											} else {
												refererCombobox.closeDropdown();
											}
										}}
										placeholder="Enter referer email"
										disabled={isSubmitting || isReApproved}
										radius="md"
										mt={8}
										onFocus={() => {
											if (
												refererState.searchQuery.trim()
											) {
												refererCombobox.openDropdown();
											}
										}}
									/>
								</Combobox.Target>

								<Combobox.Dropdown>
									<Combobox.Options
										style={{
											maxHeight: 180,
											overflowY: "auto",
										}}
									>
										{refererState.loading && (
											<Loader size="sm" />
										)}
										{!refererState.loading &&
											!refererState.error &&
											refererState.data.length === 0 &&
											refererState.searchQuery.trim() !==
												"" && (
												<Combobox.Empty>
													No users found
												</Combobox.Empty>
											)}
										{refererState.data.map(user => (
											<Combobox.Option
												key={user._id}
												value={user._id}
												disabled
												style={{ opacity: 1 }}
											>
												<UserResultItem
													user={user}
													onClick={() =>
														handleRefererSelect(
															user
														)
													}
												/>
											</Combobox.Option>
										))}
									</Combobox.Options>
								</Combobox.Dropdown>
							</Combobox>
						</Card>
					</Grid.Col>

					<Grid.Col span={{ base: 12, md: 6 }}>
						<Card
							withBorder
							radius="md"
							p="lg"
							style={{
								borderColor: "var(--mantine-color-blue-3)",
								background: "var(--mantine-color-blue-1)",
							}}
						>
							<Group justify="space-between">
								<Group>
									<div
										style={{
											width: 32,
											height: 32,
											backgroundColor:
												"var(--mantine-color-blue-6)",
											borderRadius: 8,
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
										}}
									>
										<Text c="white" size="sm" fw={600}>
											C
										</Text>
									</div>
									<Text size="lg" fw={600} c="gray.9">
										Curator
									</Text>
								</Group>
								{curatorState.selectedId && (
									<Badge
										color="green"
										variant="light"
										leftSection={
											<IconCircleCheck size={14} />
										}
									>
										Selected
									</Badge>
								)}
							</Group>

							<Text size="sm" fw={500} c="gray.7" mt="md">
								Email Address
							</Text>
							<Combobox store={curatorCombobox}>
								<Combobox.Target>
									<TextInput
										value={curatorState.searchQuery}
										onChange={e => {
											const query = e.target.value;
											setCuratorState(prev => ({
												...prev,
												searchQuery: query,
												selectedId: null,
											}));
											debouncedFetchCurators(query);
											if (query.trim()) {
												curatorCombobox.openDropdown();
											} else {
												curatorCombobox.closeDropdown();
											}
										}}
										placeholder="Enter curator email"
										disabled={isSubmitting || isReApproved}
										radius="md"
										mt={8}
										onFocus={() => {
											if (
												curatorState.searchQuery.trim()
											) {
												curatorCombobox.openDropdown();
											}
										}}
									/>
								</Combobox.Target>

								<Combobox.Dropdown>
									<Combobox.Options
										style={{
											maxHeight: 180,
											overflowY: "auto",
										}}
									>
										{curatorState.loading && (
											<Loader size="sm" />
										)}
										{!curatorState.loading &&
											!curatorState.error &&
											curatorState.data.length === 0 &&
											curatorState.searchQuery.trim() !==
												"" && (
												<Combobox.Empty>
													No users found
												</Combobox.Empty>
											)}
										{curatorState.data.map(user => (
											<Combobox.Option
												key={user._id}
												value={user._id}
												disabled
												style={{ opacity: 1 }}
											>
												<UserResultItem
													user={user}
													onClick={() =>
														handleCuratorSelect(
															user
														)
													}
												/>
											</Combobox.Option>
										))}
									</Combobox.Options>
								</Combobox.Dropdown>
							</Combobox>
						</Card>
					</Grid.Col>
				</Grid>

				<Group justify="flex-end" mt={52}>
					<Button
						variant="subtle"
						onClick={handleClose}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						onClick={() =>
							changeProfileStatus(
								isReApproved ? "re-approved" : "approved"
							)
						}
						disabled={!isFormValid || isSubmitting}
						loading={isSubmitting}
						leftSection={
							!isSubmitting ? <IconUser size={16} /> : undefined
						}
					>
						{isSubmitting ? "Approving..." : "Approve & Publish"}
					</Button>
				</Group>
			</>
		</Modal>
	);
}

function UserResultItem({
	user,
	onClick,
}: {
	user: UserRefererCrutorSearchResult;
	onClick: () => void;
}) {
	return (
		<UnstyledButton
			w="100%"
			p="sm"
			style={{
				borderRadius: "var(--mantine-radius-md)",
				transition: "background-color 150ms ease",
				cursor: "pointer",
			}}
			onMouseEnter={e => {
				e.currentTarget.style.backgroundColor =
					"var(--mantine-color-gray-1)";
			}}
			onMouseLeave={e => {
				e.currentTarget.style.backgroundColor = "";
			}}
			onClick={onClick}
		>
			<Group align="flex-start">
				<Stack gap={2} style={{ flex: 1 }}>
					<Flex align="center" justify="space-between">
						<Text size="sm" fw={600}>
							{user.firstName} {user.secondName}
						</Text>
					</Flex>

					<Group gap="xs">
						<ThemeIcon variant="light" size="sm" color="gray">
							<IconMail size={14} />
						</ThemeIcon>
						<Text size="xs">{user.email}</Text>
					</Group>
				</Stack>
			</Group>
		</UnstyledButton>
	);
}
