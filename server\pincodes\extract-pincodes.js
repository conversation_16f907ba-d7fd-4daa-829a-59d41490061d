import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dirPath = path.join(__dirname, "orignal-pincodes.json");
console.log("Reading from:", dirPath);

const raw = fs.readFileSync(dirPath, "utf-8");
const data = JSON.parse(raw);

const seen = new Set();
const cleaned = data
	.map(({ pincode }) => pincode)
	.filter(pincode => {
		if (seen.has(pincode)) return false;
		seen.add(pincode);
		return true;
	});

const outputPath = path.join(__dirname, "only-pincodes.json");
fs.writeFileSync(outputPath, JSON.stringify(cleaned, null, 2), "utf-8");

console.log("✅ Cleaned data written to:", outputPath);
