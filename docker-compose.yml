
version: '3.8'

services:
  sonarqube:
    image: sonarqube:community
    ports:
      - "8020:9000"
    environment:
      - SONAR_JDBC_URL=********************************************
      - SONAR_JDBC_USERNAME=cq_sonar
      - SONAR_JDBC_PASSWORD=cq_sonar
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_logs:/opt/sonarqube/logs
    depends_on:
      - sonarqube-db

  sonarqube-db:
    image: postgres:13
    environment:
      - POSTGRES_USER=cq_sonar
      - POSTGRES_PASSWORD=cq_sonar
      - POSTGRES_DB=cq_sonar
    volumes:
      - sonarqube_db:/var/libs/postgresql/data

volumes:
  sonarqube_data:
  sonarqube_extensions:
  sonarqube_logs:
  sonarqube_db: