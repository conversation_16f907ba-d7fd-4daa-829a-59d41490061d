import multer from "multer";
import path from "path";
import fs from "fs";

import { VIDEO_DIR, IMAGE_DIR } from "../constants/paths.js";

const storage = multer.diskStorage({
	destination: (req, file, cb) => {
		let uploadPath;

		if (file.mimetype.startsWith("image/")) {
			uploadPath = IMAGE_DIR;
		} else if (file.mimetype.startsWith("video/")) {
			uploadPath = VIDEO_DIR;
		} else {
			return cb(new Error("Invalid file type"), null);
		}
		try {
			fs.mkdirSync(uploadPath, { recursive: true });
			cb(null, uploadPath);
		} catch (error) {
			cb(error, null);
		}
	},
	filename: (req, file, cb) => {
		console.log(file.originalname);
		const ext = path.extname(file.originalname);
		const baseName = path.basename(file.originalname, ext);
		const timestamp = Date.now();

		cb(null, `${baseName}-${timestamp}${ext}`);
	},
});

export const upload = multer({ storage });
