import js from "@eslint/js";
import globals from "globals";
import prettierPlugin from "eslint-plugin-prettier";

export default [
	{
		ignores: ["dist"],
	},
	{
		files: ["**/*.js"],
		languageOptions: {
			ecmaVersion: 2020,
			sourceType: "module",
			globals: globals.node,
		},
		plugins: {
			prettier: prettierPlugin,
		},
		rules: {
			// General rules
			"no-unused-vars": ["warn", { argsIgnorePattern: "^_" }],
			"no-console": "off",
			"no-process-exit": "off",

			// Prettier integration
			"prettier/prettier": [
				"error",
				{
					useTabs: true,
					tabWidth: 4,
					singleQuote: false,
					trailingComma: "es5",
					endOfLine: "auto",
					semi: true,
					printWidth: 80,
					bracketSpacing: true,
					arrowParens: "avoid",
				},
			],
		},
	},
];
