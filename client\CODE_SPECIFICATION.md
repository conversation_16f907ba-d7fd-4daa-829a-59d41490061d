# Code Specification

This document outlines the coding standards and conventions for this React TypeScript project.

## 📋 Table of Contents

- [General Guidelines](#-general-guidelines)
- [TypeScript](#-typescript)
- [React Components](#️-react-components)
- [File Structure](#-file-structure)
- [Naming Conventions](#-naming-conventions)
- [Code Formatting](#-code-formatting)
- [ESLint Rules](#-eslint-rules)
- [Import/Export](#-importexport)
- [State Management](#-state-management)
- [API Integration](#-api-integration)
- [Testing Guidelines](#-testing-guidelines)
- [Documentation](#-documentation)
- [Development Workflow](#-development-workflow)
- [Checklist](#-checklist)

## 🌟 General Guidelines

* Write clean, readable, and maintainable code
* Follow the principle of least surprise
* Use TypeScript for all new code
* Prefer functional components with hooks over class components
* Keep components small and focused on a single responsibility
* Use meaningful variable and function names
* Write self-documenting code with minimal comments

## 🔹 TypeScript

### Type Definitions

* Define interfaces for all data structures
* Use type aliases for complex union types
* Place type definitions in `types/` directory or co-located with components
* Use generic types where appropriate

```ts
interface User {
    id: string;
    name: string;
    email: string;
}
```

### Type Safety

* Avoid `any` type - use `unknown` if necessary
* Use strict TypeScript configuration
* Leverage type inference where possible
* Use type guards for runtime type checking

## ⚛️ React Components

### Component Structure

* Use functional components with hooks
* Follow the component lifecycle pattern
* Keep JSX readable with proper indentation
* Use fragments instead of unnecessary div wrappers
* Follow consistent hook ordering within components

### Props and State

* Define prop interfaces explicitly
* Use default parameters for optional props
* Destructure props in function parameters
* Use appropriate hook types

## 📁 File Structure

### Directory Organization
```
client/
├── src/
│   ├── api/           # API integration and services
│   ├── assets/        # Static assets (images, fonts)
│   ├── components/    # Reusable React components
│   │   ├── common/    # Shared UI components
│   │   └── [feature]/ # Feature-specific components
│   ├── config/        # Configuration files
│   ├── constants/     # Application constants
│   ├── hooks/         # Custom React hooks
│   ├── layouts/       # Page layout components
│   ├── pages/         # Page components
│   ├── routes/        # Route configurations
│   ├── store/         # State management
│   ├── styles/        # Global styles and themes
│   ├── types/         # TypeScript type definitions
│   └── utils/         # Utility functions
├── public/            # Static public assets
└── tests/             # Test files
```

## 🍿 Naming Conventions

### Variables and Functions

* Use camelCase
* Use descriptive names
* Use verb-noun pattern for functions

### Components

* Use PascalCase
* Use descriptive names that indicate purpose

### Constants

* Use UPPER\_SNAKE\_CASE
* Group related constants in enums or objects

### Types and Interfaces

* Use PascalCase
* Prefix interfaces with `I` if needed for disambiguation

## 🎨 Code Formatting

### Prettier Configuration

```json
{
  "useTabs": true,
  "tabWidth": 4,
  "singleQuote": false,
  "jsxSingleQuote": false,
  "trailingComma": "es5",
  "endOfLine": "auto",
  "semi": true,
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### Formatting Rules

* Use tabs (4 spaces wide)
* Double quotes for strings
* Semicolons always
* Line limit: 80 characters
* Space inside brackets
* No parentheses for single arrow function params

## 🔍 ESLint Rules

### Key Rules Enforced

* `no-unused-vars`: Warn for unused variables (ignore `_` prefixed)
* `react-hooks/rules-of-hooks`: Enforce hooks rules
* `react-refresh/only-export-components`: Warn for non-component exports
* `jsx-a11y/anchor-is-valid`: Ensure valid anchor elements
* `jsx-a11y/no-noninteractive-element-interactions`: Prevent invalid interactions
* `prettier/prettier`: Enforce Prettier formatting

### Project-Specific Rules

```json
{
  "rules": {
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "import/order": ["error", {
      "groups": ["builtin", "external", "internal", ["parent", "sibling"], "index"],
      "pathGroups": [{ "pattern": "@/**", "group": "internal" }],
      "newlines-between": "always"
    }]
  }
}
```

## 📦 Import/Export

### Import Order

1. React
2. Third-party libraries
3. Internal imports
4. Relative imports
5. `import type` statements

### Export Patterns

* Default export components
* Named export hooks and utils
* Explicit type/interface exports

## 💄 State Management

### Local State

* `useState` for simple local state
* `useReducer` for complex state logic
* Keep state close to where it's used

## 🌐 API Integration

### Client Structure

* Dedicated client classes
* Request/response types
* Consistent error handling
* Async/await pattern

### Error Handling

* Try-catch async calls
* Show meaningful messages
* Handle edge cases

## 🧪 Testing Guidelines

* Unit test utilities
* Test behavior not implementation
* Use meaningful test names
* Mock external dependencies
* Aim for solid test coverage

## 📝 Documentation

### Code Comments

* Explain complex logic
* Focus on "why" not "what"
* Use JSDoc style for functions

### README Updates

* Document major and breaking changes
* Include setup and deployment guides

## 🔧 Development Workflow

1. Run `yarn format`
2. Ensure `yarn lint` passes
3. Test thoroughly
4. Write clear commit messages
5. Submit focused PRs
6. Adhere to spec during code review

## 📋 Checklist

* [ ] TypeScript best practices followed
* [ ] ESLint and Prettier pass
* [ ] Imports are clean and ordered
* [ ] Descriptive naming used
* [ ] Code is tested and works
* [ ] Docs updated as needed
