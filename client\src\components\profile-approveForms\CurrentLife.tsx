import React, { useState } from "react";
import CurrentLifePreview from "../profile/CurrentLifePreview";
import CurrentLifeForm from "../onBoarding-forms/CurrentLifeForm";
import type { CurrentLifeDataType } from "../../types";

interface CurrentLifeProps {
	currentLifeData: CurrentLifeDataType;
	fetchProfile: () => void;
	userId?: string;
	updatedCurrentLifeData?: CurrentLifeDataType;
	isEditable?: boolean;
}

const CurrentLife: React.FC<CurrentLifeProps> = ({
	currentLifeData,
	fetchProfile,
	userId,
	updatedCurrentLifeData,
	isEditable,
}) => {
	const [editing, setEditing] = useState(false);
	return (
		<>
			{editing ? (
				<>
					<CurrentLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={updatedCurrentLifeData ?? currentLifeData}
						userId={userId}
					/>
				</>
			) : (
				<CurrentLifePreview
					showEdit={isEditable ?? true}
					setEditing={setEditing}
					lifeData={currentLifeData}
					updatedLifeData={updatedCurrentLifeData}
					userId={userId}
				/>
			)}
		</>
	);
};

export default CurrentLife;
