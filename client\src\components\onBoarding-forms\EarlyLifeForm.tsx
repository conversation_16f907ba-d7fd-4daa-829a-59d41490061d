import {
	TextInput,
	Paper,
	Title,
	Button,
	Stack,
	Group,
	ActionIcon,
	Text,
	Flex,
	Textarea,
} from "@mantine/core";
import { TagsInput } from "@mantine/core";
import React, { useCallback, useEffect, useRef } from "react";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { IconTrash, IconX } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import type { EarlyLifeDataType } from "../../types";
import { useForm } from "@mantine/form";
import openCustomModal from "../modals/CustomModal";
import FullScreenLoader from "../FullScreenLoader";
import { useAuth } from "../../contexts/AuthContext";

interface EarlyLifeProps {
	onFormSuccess?: () => void;
	isEditable?: boolean;
	lifeData: EarlyLifeDataType | null;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	setResetForm?: (resetFunc: () => void) => void;
	setEditing?: (value: boolean) => void;
	editing?: boolean;
	fetchProfile?: () => void;
	userId?: string;
}

const EarlyLifeForm: React.FC<EarlyLifeProps> = ({
	onFormSuccess,
	lifeData,
	setHasUnsavedChanges,
	setResetForm,
	setEditing,
	editing,
	fetchProfile,
	isEditable = true,
	userId,
}) => {
	const { user, fetchUser } = useAuth();
	const prevEarlyLifeTagCount = useRef<number>(0);

	const form = useForm<EarlyLifeDataType>({
		initialValues: {
			birthCity: "",
			hometownCity: "",
			schools: [],
			universities: [],
			earlyLifeTags: [],
			videoUrl: "",
			earlyLifeSummary: "",
		},
		transformValues: values => ({
			...values,
			earlyLifeSummary: values.earlyLifeSummary?.trim() || "",
			birthCity: values.birthCity?.trim() || "",
			hometownCity: values.hometownCity?.trim() || "",
			schools: Array.isArray(values.schools)
				? values.schools.map(school => ({
						name: school?.name?.trim() || "",
						location: school?.location?.trim() || "",
						_id: school._id,
					}))
				: [],
			universities: Array.isArray(values.universities)
				? values.universities.map(university => ({
						name: university?.name?.trim() || "",
						course: university?.course?.trim() || "",
						location: university?.location?.trim() || "",
						_id: university._id,
					}))
				: [],
			earlyLifeTags: Array.isArray(values.earlyLifeTags)
				? values.earlyLifeTags.map(tag => tag?.trim() || "")
				: [],
		}),
	});

	useEffect(() => {
		const tags = form.values.earlyLifeTags;
		if (tags.length > 10) {
			if (prevEarlyLifeTagCount.current <= 10) {
				notifications.show({
					title: "Tag Limit Reached",
					message: "You can only add upto 10 tags.",
					color: "red",
					icon: <IconX />,
				});
			}

			form.setFieldValue("earlyLifeTags", tags.slice(0, 10));
		}

		prevEarlyLifeTagCount.current = tags.length;
	}, [form, form.values.earlyLifeTags]);

	const fetchData = useCallback(async () => {
		try {
			const response = await apiClient.get<EarlyLifeDataType>(
				"/api/lifeData/earlyLife"
			);
			const data = {
				...response.data,
				schools:
					response.data.schools.length > 0
						? response.data.schools
						: !isEditable
							? []
							: [{ name: "", location: "" }],
				universities:
					response.data.universities.length > 0
						? response.data.universities
						: !isEditable
							? []
							: [{ name: "", course: "", location: "" }],
			};
			form.setValues(data);
			form.setInitialValues(data);
		} catch (err) {
			console.error(`Error fetching early life data: ${err}`);
		}
	}, [isEditable]);

	useEffect(() => {
		if (lifeData) {
			const updatedLifeData = {
				...lifeData,
				schools:
					lifeData.schools.length > 0
						? lifeData.schools
						: isEditable
							? [{ name: "", location: "" }]
							: [],
				universities:
					lifeData.universities.length > 0
						? lifeData.universities
						: isEditable
							? [{ name: "", course: "", location: "" }]
							: [],
			};
			form.setValues(updatedLifeData);
			form.setInitialValues(updatedLifeData);
		} else {
			fetchData();
		}
	}, [lifeData, fetchData, isEditable]);

	const resetForm = useCallback(() => {
		form.reset();
	}, [form]);

	useEffect(() => {
		setResetForm?.(resetForm);
	}, [resetForm]);

	useEffect(() => {
		setHasUnsavedChanges?.(form.isDirty());
	}, [form.values]);

	const addSchool = () => {
		form.insertListItem("schools", { name: "", location: "" });
	};

	const removeSchool = (index: number) => {
		if (form.values.schools.length <= 1) return;
		form.removeListItem("schools", index);
	};

	const addUniversity = () => {
		form.insertListItem("universities", {
			name: "",
			course: "",
			location: "",
		});
	};

	const removeUniversity = (index: number) => {
		if (form.values.universities.length <= 1) return;
		form.removeListItem("universities", index);
	};

	const handleTrimBlur = useCallback(
		(fieldName: string) =>
			(e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
				form.setFieldValue(fieldName, e.target.value.trim());
			},
		[form]
	);

	const handleTagsChange = useCallback(
		(fieldName: string) => (tags: string[]) => {
			const trimmed = tags
				.map(tag => tag.trim())
				.filter(tag => tag.length > 0);

			form.setFieldValue(fieldName, trimmed);
		},
		[form]
	);

	const handleSubmit = async (values: EarlyLifeDataType) => {
		try {
			if (!form.isDirty() && user && user.onboardingStepCompleted) {
				notifications.show({
					title: "You’re all set",
					message: "No new changes detected since your last save.",
					color: "orange",
				});
				setEditing?.(false);
				return;
			}

			const summaryWordCount = values.earlyLifeSummary
				? values.earlyLifeSummary.trim().split(/\s+/).length
				: 0;
			if (summaryWordCount > 75) {
				notifications.show({
					title: "Validation Error",
					message: "Summary must be 75 words or fewer.",
					color: "red",
				});
				return;
			}

			const response = await apiClient.post(`/api/lifeData/update`, {
				earlyLife: values,
				userId,
			});

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			form.setInitialValues(values);
			fetchUser();
			onFormSuccess?.();
			setEditing?.(false);
			fetchProfile?.();
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message || "Failed to save data",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to save data",
					color: "red",
				});
			}
		}
	};

	const handleCancel = () => {
		form.reset();
		setEditing?.(false);
	};

	if (!form.values) return <FullScreenLoader />;

	return (
		<Paper shadow="sm" p="xl" radius="md" withBorder>
			<Flex justify={"flex-end"} mb="sm">
				{editing && (
					<Button variant="outline" onClick={handleCancel}>
						Cancel
					</Button>
				)}
			</Flex>
			<VideoPreviewAndUpload
				editing={editing}
				videoPreviewUrl={form.values.videoUrl}
				setHasUnsavedChanges={setHasUnsavedChanges}
				videoType="EarlyLife"
			/>

			<Title order={2} size="h2" mb="xl">
				Early Life Details
			</Title>
			<form>
				<Stack>
					<Textarea
						label="Early Life Summary"
						description="Max 75 words"
						autosize
						minRows={3}
						disabled={!isEditable}
						{...form.getInputProps("earlyLifeSummary")}
						onBlur={handleTrimBlur("earlyLifeSummary")}
					/>

					<TextInput
						label="Birth City"
						disabled={!isEditable}
						{...form.getInputProps("birthCity")}
						onBlur={handleTrimBlur("birthCity")}
					/>
					<TextInput
						label="Hometown City"
						disabled={!isEditable}
						{...form.getInputProps("hometownCity")}
						onBlur={handleTrimBlur("hometownCity")}
					/>

					<Title order={4}>Schools</Title>
					{form.values.schools.length > 0 ? (
						form.values.schools.map((_, index) => (
							<Group key={index} align="end" gap="sm">
								<TextInput
									label="Name"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`schools.${index}.name`
									)}
									onBlur={handleTrimBlur(
										`schools.${index}.name`
									)}
								/>
								<TextInput
									label="Location"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`schools.${index}.location`
									)}
									onBlur={handleTrimBlur(
										`schools.${index}.location`
									)}
								/>
								{isEditable && (
									<ActionIcon
										variant="subtle"
										color="red"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeSchool(index),
											});
										}}
										disabled={
											form.values.schools.length === 1
										}
										style={{ alignSelf: "flex-end" }}
									>
										<IconTrash size={16} />
									</ActionIcon>
								)}
							</Group>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No school to display.
						</Text>
					)}

					{isEditable && (
						<Button variant="light" onClick={addSchool}>
							+ Add School
						</Button>
					)}

					<Title order={4}>Universities</Title>
					{form.values.universities.length > 0 ? (
						form.values.universities.map((_, index) => (
							<Group key={index} align="end" gap="sm">
								<TextInput
									label="Name"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`universities.${index}.name`
									)}
									onBlur={handleTrimBlur(
										`universities.${index}.name`
									)}
								/>
								<TextInput
									label="Course"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`universities.${index}.course`
									)}
									onBlur={handleTrimBlur(
										`universities.${index}.course`
									)}
								/>
								<TextInput
									label="Location"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`universities.${index}.location`
									)}
									onBlur={handleTrimBlur(
										`universities.${index}.location`
									)}
								/>
								{isEditable && (
									<ActionIcon
										variant="subtle"
										color="red"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeUniversity(index),
											});
										}}
										disabled={
											form.values.universities.length ===
											1
										}
										style={{ alignSelf: "flex-end" }}
									>
										<IconTrash size={16} />
									</ActionIcon>
								)}
							</Group>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No university to display.
						</Text>
					)}

					{isEditable && (
						<Button variant="light" onClick={addUniversity}>
							+ Add University
						</Button>
					)}

					<TagsInput
						label="Early Life Tags"
						placeholder={
							isEditable ? "Add a tag and press Enter" : ""
						}
						disabled={!isEditable}
						{...form.getInputProps("earlyLifeTags")}
						onChange={handleTagsChange("earlyLifeTags")}
					/>

					{isEditable && (
						<Group justify="flex-end">
							<Button
								w={100}
								onClick={() => form.onSubmit(handleSubmit)()}
							>
								Save
							</Button>
						</Group>
					)}
				</Stack>
			</form>
		</Paper>
	);
};

export default EarlyLifeForm;
