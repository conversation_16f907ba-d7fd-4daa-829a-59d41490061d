import { useParams, useSearchParams } from "react-router-dom";
import {
	<PERSON>ton,
	Card,
	Badge,
	Avatar,
	Text,
	Group,
	Title,
	SimpleGrid,
	Container,
	Grid,
	Box,
	ThemeIcon,
	Stack,
	Flex,
	Tabs,
	BackgroundImage,
	useMantineTheme,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { IconUsers, IconUserOff, IconCalendar } from "@tabler/icons-react";
import unavailableImage from "@assets/unavailable-image.png";
import FullScreenLoader from "../../FullScreenLoader";
import { useCallback, useEffect, useMemo, useState } from "react";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import apiClient from "../../../config/axios";
import type {
	EventAttendanceStatusType,
	EventDetailsType,
	EventUser,
} from "../../../types";
import EllipsisCell from "../../EllipsisCell";
import EventImageCards from "../EventImageCards";
import "react-quill-new/dist/quill.snow.css";
import EventAttendeesModal from "./EventAttendeesModal";
import { EventAttendanceStatus } from "../../../constants";
import FeedWrapper from "../event-feed/FeedWrapper";
import CustomQuill from "../../quill/CustomQuill";

const formatDateTimeRange = (start: Date | string, end: Date | string) => {
	const startDate = new Date(start);
	const endDate = new Date(end);

	const startDateString = startDate.toLocaleDateString("en-US", {
		weekday: "long",
		year: "numeric",
		month: "long",
		day: "numeric",
	});

	const startTimeString = startDate.toLocaleTimeString("en-US", {
		hour: "numeric",
		minute: "2-digit",
		hour12: true,
	});

	const endDateString = endDate.toLocaleDateString("en-US", {
		weekday: "long",
		year: "numeric",
		month: "long",
		day: "numeric",
	});

	const endTimeString = endDate.toLocaleTimeString("en-US", {
		hour: "numeric",
		minute: "2-digit",
		hour12: true,
	});

	if (startDateString === endDateString) {
		return {
			date: startDateString,
			time: `${startTimeString} - ${endTimeString}`,
		};
	} else {
		return {
			date: `${startDateString} - ${endDateString}`,
			time: `${startTimeString} - ${endTimeString}`,
		};
	}
};

const EventDetails = () => {
	const eventId = useParams().eventId;
	const [searchParams, setSearchParams] = useSearchParams();

	const [event, setEvent] = useState<EventDetailsType | null>(null);
	const [loading, setLoading] = useState<boolean>(false);

	const [activeTab, setActiveTab] = useState<string | null>(
		searchParams.get("activeTab") || "info"
	);

	const [modalOpened, setModalOpened] = useState(false);
	const [modalTitle, setModalTitle] = useState("");
	const [modalUsers, setModalUsers] = useState<EventUser[]>([]);
	const [loadingUsers, setLoadingUsers] = useState(false);

	const handleOpenModal = async (type: EventAttendanceStatusType) => {
		if (
			type === EventAttendanceStatus.Attending &&
			event?.counts.attendeesCount === 0
		) {
			notifications.show({
				title: "No Attendees",
				message: "There are no attendees for this event",
				color: "red",
			});
			return;
		} else if (
			type === EventAttendanceStatus.NotAttending &&
			event?.counts.nonAttendingCount === 0
		) {
			notifications.show({
				title: "No Non-Attendees",
				message: "There are no non-attendees for this event",
				color: "red",
			});
			return;
		}
		setModalTitle(
			type === "attending" ? "Attendees Users" : "Non-Attendees Users"
		);
		setModalOpened(true);
		setLoadingUsers(true);

		try {
			const response = await apiClient.get<EventUser[]>(
				`/api/events/${eventId}/${type}`
			);
			setModalUsers(response.data);
		} catch (error) {
			console.error("Failed to fetch users", error);
			notifications.show({
				title: "Error",
				message: "Failed to fetch users",
				color: "red",
			});
		} finally {
			setLoadingUsers(false);
		}
	};

	useEffect(() => {
		const currentTab = searchParams.get("activeTab");
		if (activeTab && currentTab !== activeTab) {
			searchParams.set("activeTab", activeTab);
			setSearchParams(searchParams);
		}
	}, [activeTab, searchParams, setSearchParams]);

	const fetchEvent = useCallback(async () => {
		try {
			setLoading(true);
			const res = await apiClient.get<EventDetailsType>(
				`/api/events/get-event/${eventId}`
			);
			setEvent(res.data);
			notifications.show({
				title: "Success",
				message: "Event fetched successfully",
				color: "green",
			});
		} catch (err) {
			console.log(err);
			if (isAxiosError(err)) {
				notifications.show({
					title: "Failed",
					message:
						err.response?.data?.message || "Failed to fetch event",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to fetch event",
					color: "red",
				});
			}
		} finally {
			setLoading(false);
		}
	}, [eventId]);

	useEffect(() => {
		fetchEvent();
	}, [fetchEvent]);

	const { thumbnailImage } = useMemo(() => {
		if (!event) {
			return { thumbnailImage: unavailableImage };
		}
		const thumbnailImage = event.thumbnailImage?.url || unavailableImage;
		return { thumbnailImage };
	}, [event]);

	useEffect(() => {
		if (
			activeTab === "moments" &&
			(event?.eventTypes.isUpcoming || event?.eventTypes.isDraft)
		) {
			setActiveTab("info");
		}
	}, [activeTab, event?.eventTypes.isDraft, event?.eventTypes.isUpcoming]);

	const theme = useMantineTheme();
	const isLargeScreen = useMediaQuery(`(min-width: ${theme.breakpoints.lg})`);

	if (loading || !event) {
		return <FullScreenLoader />;
	}

	const sidebar = (
		<SidebarCard
			event={event}
			isUpcoming={event.eventTypes.isUpcoming}
			onOpenModal={handleOpenModal}
			isSticky={isLargeScreen}
		/>
	);

	return (
		<Container size="xl" py={"lg"}>
			<EventAttendeesModal
				opened={modalOpened}
				onClose={() => setModalOpened(false)}
				title={modalTitle}
				users={modalUsers}
				loading={loadingUsers}
			/>
			<Card padding={0} shadow="lg" radius="md" withBorder>
				<Box
					style={{
						position: "relative",
					}}
				>
					<BackgroundImage
						src={thumbnailImage}
						radius="md"
						className={`!h-64 ${thumbnailImage === unavailableImage ? "!bg-contain" : "!bg-cover"} !bg-center !bg-no-repeat`}
					/>
					<Box
						style={{
							position: "absolute",
							top: 0,
							right: 0,
							bottom: 0,
							left: 0,
							background:
								"linear-gradient(to top, rgba(0,0,0,0.6), transparent)",
						}}
					/>
					<Stack
						style={{
							position: "absolute",
							top: "1.5rem",
							left: "1.5rem",
						}}
						c={"white"}
					>
						{event.eventTypes.isUpcoming && (
							<Badge variant="filled" size="lg">
								Upcoming
							</Badge>
						)}
						<Stack gap={4}>
							<Title order={1} lineClamp={1}>
								{event.name}
							</Title>
							<Text size="md" lineClamp={2}>
								{event.description}
							</Text>
						</Stack>
					</Stack>
				</Box>
			</Card>

			<Grid gutter="lg" mt="xl">
				<Grid.Col span={{ base: 12, lg: 8 }}>
					<Tabs
						variant="pills"
						value={activeTab}
						onChange={setActiveTab}
					>
						{!(
							event.eventTypes.isUpcoming ||
							event.eventTypes.isDraft
						) && (
							<Tabs.List grow mb={"lg"}>
								<Tabs.Tab value="info">Information</Tabs.Tab>
								<Tabs.Tab value="moments">Moments</Tabs.Tab>
							</Tabs.List>
						)}
						<Tabs.Panel value="info">
							<SimpleGrid cols={1} spacing="lg">
								{!isLargeScreen && sidebar}
								<Card shadow="lg" p="xl" radius="md" withBorder>
									<Title order={2} mb="lg">
										About This Event
									</Title>

									<CustomQuill
										value={event.detail}
										modules={{ toolbar: false }}
										readOnly={true}
										customStyle={`
										.ql-container.ql-snow {
											border: none;
										}
										.ql-editor {
											padding: 0;
										} 
									`}
									/>
								</Card>
								{event.creationMedia.length > 0 && (
									<>
										<Card
											shadow="lg"
											p="xl"
											radius="md"
											withBorder
										>
											<Title order={2} mb="lg">
												Event Highlights
											</Title>
											<EventImageCards
												gridProps={{
													cols: { base: 2, sm: 3 },
													spacing: "md",
												}}
												images={event.creationMedia}
												isEditMode={false}
											/>
										</Card>
									</>
								)}
							</SimpleGrid>
						</Tabs.Panel>
						<Tabs.Panel value="moments">
							<FeedWrapper />
						</Tabs.Panel>
					</Tabs>
				</Grid.Col>
				{isLargeScreen && <Grid.Col span={4}>{sidebar}</Grid.Col>}
			</Grid>
		</Container>
	);
};

type SidebarCardProps = {
	event: EventDetailsType;
	isUpcoming: boolean;
	onOpenModal: (type: EventAttendanceStatusType) => void;
};

const SidebarCard = ({ event, isUpcoming, onOpenModal }: SidebarCardProps) => {
	const { date, time } = formatDateTimeRange(event.startDate, event.endDate);
	const [counts, setCounts] = useState(event.counts);
	const [currentUserAttendingStatus, setCurrentUserAttendingStatus] =
		useState(event.currentUserAttendingStatus);
	const [loading, setLoading] = useState(false);

	const handleRespond = async (status: EventAttendanceStatusType) => {
		setLoading(true);
		try {
			const resposne = await apiClient.post(
				`/api/events/attend/${event._id}`,
				{
					status,
				}
			);
			notifications.show({
				title: "Success",
				message: "Your response has been recorded",
				color: "green",
			});

			setCounts(prevCounts => {
				return resposne.data.counts ?? prevCounts;
			});
			setCurrentUserAttendingStatus(status);
		} catch (err) {
			console.error(err);
			if (isAxiosError(err)) {
				notifications.show({
					title: "Failed",
					message:
						err.response?.data?.message ||
						"Failed to record your response",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to record your response",
					color: "red",
				});
			}
		} finally {
			setLoading(false);
		}
	};

	return (
		<Box style={{ position: "sticky", top: 112 }}>
			<Card shadow="lg" p="xl" radius="md" withBorder>
				<Stack gap="lg">
					<Flex justify={"space-between"} align={"center"}>
						<Title order={3}>Event Details</Title>
						{currentUserAttendingStatus === "attending" && (
							<Badge color="green">Attending</Badge>
						)}
						{currentUserAttendingStatus === "not_attending" && (
							<Badge color="red">Not Attending</Badge>
						)}
					</Flex>

					<Stack gap="xs">
						<Flex align="flex-start" gap="xs">
							<ThemeIcon variant="light" size="lg">
								<IconCalendar size={20} />
							</ThemeIcon>
							<div>
								<Text fw={600}>{date}</Text>
								<Text size="sm" c="dimmed">
									{time}
								</Text>
							</div>
						</Flex>
					</Stack>

					<SimpleGrid cols={2} spacing="md">
						<Card
							p="md"
							radius="md"
							withBorder
							style={{
								textAlign: "center",
								cursor: "pointer",
							}}
							onClick={() => onOpenModal("attending")}
						>
							<ThemeIcon
								size="lg"
								radius="lg"
								variant="light"
								mx="auto"
								mb="sm"
							>
								<IconUsers size={20} />
							</ThemeIcon>
							<Text fz={28} fw={700}>
								{counts.attendeesCount ?? 0}
							</Text>
							<Text size="xs" c="dimmed">
								Attendees
							</Text>
						</Card>
						<Card
							p="md"
							radius="md"
							withBorder
							style={{
								textAlign: "center",
								cursor: "pointer",
							}}
							onClick={() => onOpenModal("not_attending")}
						>
							<ThemeIcon
								size="lg"
								radius="lg"
								variant="light"
								mx="auto"
								mb="sm"
							>
								<IconUserOff size={20} />
							</ThemeIcon>
							<Text fz={28} fw={700}>
								{counts.nonAttendingCount ?? 0}
							</Text>
							<Text size="xs" c="dimmed">
								Non-Attendees
							</Text>
						</Card>
					</SimpleGrid>

					<Box
						style={{
							borderTop: "1px solid #e9ecef",
							paddingTop: "var(--mantine-spacing-lg)",
						}}
					>
						<Text
							size="sm"
							fw={500}
							c="dimmed"
							tt="uppercase"
							mb="sm"
						>
							Organized by
						</Text>
						<Group wrap="nowrap">
							<Avatar
								src={event.createdBy.image}
								size="lg"
								radius="xl"
							>
								{[
									event.createdBy.firstName,
									event.createdBy.secondName,
								]
									.filter(Boolean)
									.map(name => name.split(" ")[0][0])
									.join("")}
							</Avatar>

							<div>
								<EllipsisCell
									value={`${event.createdBy.firstName} ${event.createdBy.secondName}`}
									maxWidth={100}
								/>
								<Text size="sm" c="dimmed">
									{event.createdBy.email}
								</Text>
							</div>
						</Group>
					</Box>

					{isUpcoming && (
						<>
							{currentUserAttendingStatus === "attending" && (
								<Button
									variant="outline"
									color="red"
									onClick={() =>
										handleRespond("not_attending")
									}
									loading={loading}
									fullWidth
								>
									Leave Event
								</Button>
							)}

							{currentUserAttendingStatus === "not_attending" && (
								<Button
									variant="gradient"
									gradient={{
										from: "blue",
										to: "cyan",
									}}
									onClick={() => handleRespond("attending")}
									loading={loading}
									fullWidth
								>
									Join Event
								</Button>
							)}

							{currentUserAttendingStatus === undefined && (
								<>
									<Button
										variant="gradient"
										gradient={{
											from: "blue",
											to: "cyan",
										}}
										onClick={() =>
											handleRespond("attending")
										}
										loading={loading}
										fullWidth
									>
										Join Event
									</Button>
									<Button
										fullWidth
										color="gray"
										variant="outline"
										onClick={() =>
											handleRespond("not_attending")
										}
										loading={loading}
									>
										Can't Attend
									</Button>
								</>
							)}
						</>
					)}
				</Stack>
			</Card>
		</Box>
	);
};

export default EventDetails;
