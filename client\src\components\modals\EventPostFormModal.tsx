import {
	ActionIcon,
	Button,
	Divider,
	Group,
	Modal,
	ScrollArea,
	Stack,
	Text,
	Title,
	Tooltip,
	Image,
	SimpleGrid,
	Card,
} from "@mantine/core";
import CustomQuill from "../quill/CustomQuill";
import { useForm } from "@mantine/form";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { IconPhoto, IconX } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { AnimatePresence, motion } from "framer-motion";
import unavailableImage from "@assets/unavailable-image.png";
import { GalleryViewer } from "../GalleryViewer";
import { useHover } from "@mantine/hooks";
import { isAxiosError } from "axios";
import FullScreenLoader from "../FullScreenLoader";
import apiClient from "../../config/axios";
import "quill-mention/autoregister";
import type { EventUser } from "../../types";
import { resolveImageUrl } from "../../utils/imageUrl";

type EventPostFormModalProps = {
	opened: boolean;
	onClose: () => void;
	postFormData?: {
		content: string;
		media: { S3Key: string; key: string; type: string }[];
	};
	eventId: string;
};

type MentionUser = {
	id: string;
	value: string;
	email: string;
	image: string;
};

const defaultCustomModules = {
	toolbar: [
		"bold",
		"italic",
		"underline",
		"strike",
		{ list: "ordered" },
		{ list: "bullet" },
		"link",
		{ color: [], background: [] },
		"clean",
	],
};

const extractMentionedUserIds = (content: string): string[] => {
	try {
		const mentionRegex =
			/<span[^>]*class="mention"[^>]*data-id="([^"]*)"[^>]*>/g;
		const userIds: string[] = [];
		let match;

		while ((match = mentionRegex.exec(content)) !== null) {
			if (match[1] && !userIds.includes(match[1])) {
				userIds.push(match[1]);
			}
		}
		return userIds;
	} catch (error) {
		console.error("Error extracting mentioned user IDs:", error);
		return [];
	}
};

const MAX_FILES = 25;
const QUILL_MIN_HEIGHT = "250px";
// const QUILL_MAX_HEIGHT = "280px";

const generatePreviewKey = (): string =>
	`preview-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

const ImageItem = ({
	url,
	index,
	onRemove,
	onPreview,
}: {
	url: string;
	index: number;
	onRemove: (index: number) => void;
	onPreview: (index: number) => void;
}) => {
	const { hovered, ref } = useHover();

	const handleRemove = useCallback(() => onRemove(index), [onRemove, index]);
	const handlePreview = useCallback(
		() => onPreview(index),
		[onPreview, index]
	);

	const imageStyle = useMemo(
		() => ({
			filter: hovered ? "brightness(90%)" : "brightness(100%)",
			cursor: "pointer",
		}),
		[hovered]
	);

	const actionIconStyle = useMemo(
		() => ({
			position: "absolute" as const,
			top: 4,
			right: 4,
			opacity: hovered ? 1 : 0,
			transition: "opacity 0.2s",
			pointerEvents: hovered ? ("auto" as const) : ("none" as const),
		}),
		[hovered]
	);

	return (
		<motion.div
			ref={ref}
			layout
			initial={{ opacity: 0, scale: 1 }}
			animate={{
				opacity: 1,
				scale: 1,
				transition: {
					duration: 0.3,
					delay: 0.1 * index,
				},
			}}
			exit={{ opacity: 0, scale: 0.95 }}
		>
			<Card p={0} shadow="md" withBorder radius="md">
				<Image
					src={url}
					alt={`Preview ${index}`}
					fallbackSrc={unavailableImage}
					h="100%"
					w="100%"
					styles={{
						root: {
							aspectRatio: "185/100",
						},
					}}
					fit="cover"
					radius="md"
					style={imageStyle}
					onClick={handlePreview}
				/>

				<ActionIcon
					color="red"
					radius="xl"
					size="md"
					onClick={handleRemove}
					style={actionIconStyle}
				>
					<IconX size={16} />
				</ActionIcon>
			</Card>
		</motion.div>
	);
};

const EventPostFormModal = (props: EventPostFormModalProps) => {
	const { opened, onClose, postFormData, eventId } = props;
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [loading, setLoading] = useState<boolean>(false);
	const [files, setFiles] = useState<File[]>([]);
	const [previewUrls, setPreviewUrls] = useState<string[]>([]);
	const [previewKeys, setPreviewKeys] = useState<string[]>([]);
	const [viewerOpened, setViewerOpened] = useState<number | null>(null);

	const [uploadedMedia, setUploadedMedia] = useState<
		{ S3Key: string; key: string; type: string }[]
	>([]);

	const handlePreview = useCallback((index: number) => {
		setViewerOpened(index);
	}, []);

	const handleCloseViewer = useCallback(() => {
		setViewerOpened(null);
	}, []);

	const form = useForm({
		initialValues: {
			content: postFormData?.content || "",
		},
		validate: {
			content: value => {
				const textContent = value.replace(/<[^>]*>/g, "").trim();
				if (!textContent && uploadedMedia.length === 0) {
					return "Please add some content or media to your post";
				}
				return null;
			},
		},
	});

	const getSignedUrl = useCallback(
		async ({
			eventId,
			fileName,
			fileType,
		}: {
			eventId: string;
			fileName: string;
			fileType: string;
		}) => {
			const response = await apiClient.get(
				`/api/events/signed-url/event-post/${eventId}`,
				{
					params: {
						fileName,
						fileType,
					},
				}
			);
			return response.data;
		},
		[]
	);

	const handleUpload = useCallback(
		async (filesToUpload: File[]) => {
			try {
				const uploadPromises = filesToUpload.map(async file => {
					const { uploadUrl, S3Key } = await getSignedUrl({
						eventId,
						fileName: file.name,
						fileType: file.type,
					});

					await fetch(uploadUrl, {
						method: "PUT",
						headers: {
							"Content-Type": file.type,
						},
						body: file,
					});

					const fileType: "image" | "video" = file.type.startsWith(
						"image/"
					)
						? "image"
						: "video";

					return { S3Key, key: file.name, type: fileType };
				});

				const newMedia = await Promise.all(uploadPromises);
				setUploadedMedia(prev => [...prev, ...newMedia]);
			} catch (error) {
				console.error("File upload error:", error);
				if (isAxiosError(error)) {
					notifications.show({
						title: "File Upload Failed",
						message: error.response?.data?.message || error.message,
						color: "red",
					});
					return;
				}
				notifications.show({
					title: "File Upload Failed",
					message:
						"There was an error uploading your files. Please try again.",
					color: "red",
				});
			}
		},
		[eventId, getSignedUrl]
	);

	const handleFileChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			const selectedFiles = event.target.files;
			if (!selectedFiles) return;

			if (files.length + selectedFiles.length > MAX_FILES) {
				notifications.show({
					title: "File Limit Reached",
					message: `You can only upload a maximum of ${MAX_FILES} files.`,
					color: "red",
				});
				return;
			}

			const invalidFiles = Array.from(selectedFiles).filter(
				file => !file.type.startsWith("image/")
			);

			if (invalidFiles.length > 0) {
				notifications.show({
					title: "Invalid File Type",
					message: "Please upload only images",
					color: "red",
				});
				return;
			}

			const newFiles = Array.from(selectedFiles);
			setFiles(prev => [...prev, ...newFiles]);

			const newPreviewUrls = newFiles.map(file =>
				URL.createObjectURL(file)
			);
			setPreviewUrls(prev => [...prev, ...newPreviewUrls]);

			// Generate unique keys for new files
			const newKeys = newFiles.map(() => generatePreviewKey());
			setPreviewKeys(prev => [...prev, ...newKeys]);

			handleUpload(newFiles);
		},
		[files.length, handleUpload]
	);

	const handleRemoveFile = useCallback(
		(index: number) => {
			if (previewUrls[index]) {
				URL.revokeObjectURL(previewUrls[index]);
			}

			setFiles(prev => prev.filter((_, i) => i !== index));
			setPreviewUrls(prev => prev.filter((_, i) => i !== index));
			setPreviewKeys(prev => prev.filter((_, i) => i !== index));
			setUploadedMedia(prev => prev.filter((_, i) => i !== index));

			// Clear file input to allow re-uploading the same file
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		},
		[previewUrls]
	);

	const handleSubmit = useCallback(
		async (e: React.FormEvent) => {
			e.preventDefault();

			// Validate form
			const validation = form.validate();
			if (validation.hasErrors) {
				return;
			}

			try {
				setLoading(true);

				// Extract mentioned user IDs from content
				const mentionedUserIds = extractMentionedUserIds(
					form.values.content
				);

				const response = await apiClient.post(
					`/api/events/create-event-post/${eventId}`,
					{
						content: form.values.content,
						mediaFiles: uploadedMedia,
						userTags: mentionedUserIds,
					}
				);

				notifications.show({
					title: "Post Created",
					message: "Your event post has been created successfully.",
					color: "green",
				});

				onClose();
				return response.data;
			} catch (error) {
				console.error("Error creating post:", error);
				if (isAxiosError(error)) {
					notifications.show({
						title: "Error Creating Post",
						message: error.response?.data?.message || error.message,
						color: "red",
					});
				} else {
					notifications.show({
						title: "Error Creating Post",
						message: "There was an error creating your post.",
						color: "red",
					});
				}
			} finally {
				setLoading(false);
			}
		},
		[eventId, form, uploadedMedia, onClose]
	);

	useEffect(() => {
		if (!opened) {
			previewUrls.forEach(url => URL.revokeObjectURL(url));

			setFiles([]);
			setPreviewUrls([]);
			setPreviewKeys([]);
			setUploadedMedia([]);
			form.reset();
		}
	}, [opened, form, previewUrls]);

	const mentionSource = useCallback(
		async (
			searchTerm: string,
			renderList: (list: MentionUser[], searchTerm: string) => void
		) => {
			try {
				const response = await apiClient.get(
					`/api/events/event-post/mentions?searchTerm=${searchTerm}`
				);

				const values: MentionUser[] = response.data.map(
					(user: EventUser) => ({
						id: user._id,
						value: `${user.firstName} ${user.secondName ?? ""}`.trim(),
						email: user.email,
						image: user.image,
					})
				);

				if (searchTerm.length === 0) {
					renderList(values, searchTerm);
				} else {
					const matches = values.filter((v: MentionUser) =>
						v.value.toLowerCase().includes(searchTerm.toLowerCase())
					);
					renderList(matches, searchTerm);
				}
			} catch (error) {
				console.error("Mention fetch failed:", error);
				renderList([], searchTerm);
			}
		},
		[]
	);

	const quillModules = useMemo(
		() => ({
			...defaultCustomModules,
			mention: {
				allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
				mentionDenotationChars: ["@"],
				source: mentionSource,

				fixMentionsToQuill: false,
				defaultMenuOrientation: "bottom",

				mentionContainerClass: "mention-container",
				mentionListClass: "mention-list",
				renderItem: (item: MentionUser) => {
					const itemContainer = document.createElement("div");
					itemContainer.className = "mention-list-item";

					const avatar = document.createElement("img");
					avatar.src =
						resolveImageUrl(item.image) ??
						`https://api.dicebear.com/5.x/initials/svg?seed=${item.value}`;
					avatar.alt = item.value;
					avatar.className = "mention-avatar";
					avatar.onerror = () => {
						if (
							avatar.src !==
							`https://api.dicebear.com/5.x/initials/svg?seed=${item.value}`
						) {
							avatar.src = `https://api.dicebear.com/5.x/initials/svg?seed=${item.value}`;
						}
					};

					const infoContainer = document.createElement("div");
					infoContainer.className = "mention-info";

					const name = document.createElement("div");
					name.className = "mention-name";
					name.textContent = item.value;

					const email = document.createElement("div");
					email.className = "mention-email";
					email.textContent = item.email;

					infoContainer.appendChild(name);
					infoContainer.appendChild(email);

					itemContainer.appendChild(avatar);
					itemContainer.appendChild(infoContainer);

					return itemContainer;
				},
			},
		}),
		[mentionSource]
	);

	const imageGrid = useMemo(() => {
		if (previewUrls.length === 0) return null;

		return (
			<SimpleGrid cols={3}>
				<AnimatePresence>
					{previewUrls.map((url, index) => (
						<ImageItem
							key={previewKeys[index] || index}
							url={url}
							index={index}
							onRemove={handleRemoveFile}
							onPreview={handlePreview}
						/>
					))}
				</AnimatePresence>
			</SimpleGrid>
		);
	}, [previewUrls, previewKeys, handleRemoveFile, handlePreview]);

	return (
		<>
			{loading && <FullScreenLoader />}
			<Modal
				opened={opened}
				onClose={onClose}
				title={
					<Stack gap={0}>
						<Title order={3}>
							{postFormData ? "Update Post" : "Create Post"}
						</Title>
						<Text c={"dimmed"} size="sm">
							Share your thoughts and experiences with the
							community
						</Text>
					</Stack>
				}
				size="xl"
				scrollAreaComponent={ScrollArea.Autosize}
				padding="lg"
				withCloseButton={false}
				trapFocus={false}
				closeOnEscape={false}
			>
				<form onSubmit={handleSubmit}>
					<Stack gap="lg">
						<CustomQuill
							value={form.values.content}
							customStyle={`
								.ql-container.ql-snow {
									border: 1px solid #ccc !important;
									border-top: none !important;
								}
								.ql-editor {
									padding: 12px 15px !important;
								}
								.mention-container {
									background-color: var(--mantine-color-body);
									border-radius: var(--mantine-radius-md);
									box-shadow: var(--mantine-shadow-lg);
									overflow: hidden;
									border: 1px solid var(--mantine-color-gray-3);
								}

								.mention-list {
									max-height: 250px;
									overflow-y: auto;
									padding: 0;
									margin: 0;
								}

								.mention-list-item {
									display: flex;
									align-items: center;
									padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
									cursor: pointer;
									border-bottom: 1px solid var(--mantine-color-gray-2);
								}

								.mention-list-item:last-child {
									border-bottom: none;
								}

								.mention-list-item.selected,
								.mention-list-item:hover {
									background-color: var(--mantine-color-gray-0);
								}

								.mention-avatar {
									width: 36px;
									height: 36px;
									border-radius: 50%;
									margin-right: var(--mantine-spacing-sm);
									object-fit: cover;
								}

								.mention-info {
									display: flex;
									flex-direction: column;
								}

								.mention-name {
									font-weight: 500;
									color: var(--mantine-color-black);
								}

								.mention-email {
									font-size: var(--mantine-font-size-sm);
									color: var(--mantine-color-dimmed);
								}
							`}
							minHeight={QUILL_MIN_HEIGHT}
							onChange={val => form.setFieldValue("content", val)}
							modules={quillModules}
						/>

						{imageGrid}

						<Tooltip label="Add Media">
							<ActionIcon
								onClick={() => fileInputRef.current?.click()}
								variant="subtle"
							>
								<IconPhoto size={32} />
							</ActionIcon>
						</Tooltip>
						<input
							accept="image/*"
							multiple
							onChange={handleFileChange}
							ref={fileInputRef}
							style={{ display: "none" }}
							type="file"
						/>
					</Stack>

					<Divider mt="xl" />

					<Group justify="flex-end" mt="lg">
						<Button onClick={onClose} variant="outline">
							Cancel
						</Button>
						<Button type="submit" loading={loading}>
							Post
						</Button>
					</Group>
				</form>
			</Modal>

			{viewerOpened !== null && (
				<GalleryViewer
					opened={viewerOpened !== null}
					onClose={handleCloseViewer}
					images={previewUrls}
					initial={viewerOpened || 0}
				/>
			)}
		</>
	);
};

export default EventPostFormModal;
