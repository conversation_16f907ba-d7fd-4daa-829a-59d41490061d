# SM 360

**SM 360** is a platform that enables users to upload videos and build a structured, AI-enriched profile that captures their life story across key phases — early life, professional journey, and current experiences.

---

## What is SM 360?

SM 360 empowers individuals to:

* Upload life stories across phases like early life, professional journey, and current status
* Build rich personal profiles using OpenAI-powered data extraction
* Complete onboarding by uploading themed videos
* Create new users or explore the life journeys of others
* Track and review profile changes with admin oversight

---

## What's Done

* Onboarding flow with 3 video upload steps:

  * Early Life
  * Professional Life
  * Current Life
* AI-based video content extraction using OpenAI
* Automatic tagging and profile enrichment
* User directory listing after onboarding
* Profile update functionality post-onboarding
* Admin support for creating new user profiles
* Difference tracking for life data changes
* Enhanced search functionality across all user fields
* Video instruction modals with reusable components
* Smooth scroll validation for form errors

---

## User Roles

* **Community Members**: Upload personal videos, create and edit life story profiles
* **Admins**: Review and approve uploads, manage users
* **Super Admins**: Full administrative access and user management

---

## Key Features

### For Community Members

* Upload videos for each phase of life
* Profile management (education, jobs, current city, etc.)
* AI-based data extraction and tagging
* Integration with social platforms like LinkedIn, Instagram, Twitter

### For Admins

* Approve new users after onboarding
* Manage users and content via dashboard
* Role-based access control
* View detailed change tracking for user profiles
* Enhanced search across user data (name, email, tags)

---

## Onboarding Flow

The onboarding process consists of 10 steps, guiding users through profile creation and video uploads:

![Video Upload and Process Flow](server/docAssets/vidoe-upload-and-process.png)

### Step-by-Step Process

1. **Basic Details (Step 1)**
   * User provides initial profile information
   * Progresses to Early Life Video upload

2. **Early Life Video Upload (Step 2)**
   * User uploads video sharing early life experiences
   * AI processes video for transcription and data extraction
   * Progresses to Early Life Form

3. **Early Life Form (Step 3)**
   * Review and edit AI-extracted data:
     * Birth city and hometown
     * School and university details
     * Up to 10 tags (hobbies, locations, sports, etc.)
     * Personal summary (≤ 75 words)
   * Progresses to Professional Life Video

4. **Professional Life Video Upload (Step 4)**
   * User uploads video about professional journey
   * AI processes video content
   * Progresses to Professional Life Form

5. **Professional Life Form (Step 5)**
   * Review and edit extracted professional data:
     * First company and role details
     * Subsequent companies and experiences
     * Professional tags (domains, tools, projects, skills)
     * Personal summary (≤ 75 words)
   * Progresses to Current Life Video

6. **Current Life Video Upload (Step 6)**
   * User uploads video about current status
   * AI processes current life content
   * Progresses to Current Life Form

7. **Current Life Form (Step 7)**
   * Review and edit current life data:
     * Current city and organization
     * Personal summary (≤ 75 words)
     * Current life tags
   * Progresses to Final Submit

8. **Final Submit (Step 8)**
   * User submits complete profile for review
   * System validates all three videos are processed
   * Progresses to Wait for Approval

9. **Wait for Approval (Step 9)**
   * Profile pending admin review
   * Admin can approve, reject, or request changes
   * User receives email notifications

10. **Completed (Step 10)**
    * Profile approved and listed in user directory
    * User gains full platform access
    * Can update profile information post-approval

### Key Features

* **AI-Powered Extraction**: OpenAI automatically extracts structured data from videos
* **Real-time Processing**: Video transcription and data extraction happen in background
* **Form Validation**: Each step validates required information before progression
* **Progress Tracking**: Users can see their current step and completed phases
* **Admin Oversight**: All profiles require admin approval before going live
* **Change Tracking**: Post-approval edits are tracked for admin review

---

## Project Structure

```
sm-360/
├── client/          # React frontend
│   └── src/
│       ├── pages/
│       ├── components/
│       ├── contexts/
│       └── types/
└── server/          # Node.js backend
    ├── routes/
    ├── models/
    ├── services/
    └── config/
```

---

## Getting Started

### Prerequisites

* Node.js (v18+)
* MongoDB
* Docker (for Mailpit email testing)
* AWS S3 (for file storage)

### Setup

```bash
# Clone the repository
<NAME_EMAIL>:CQ-Dev-Team/SM360.git
cd SM360
```

#### Backend

```bash
cd server
npm install
cp .env.example .env
npm start
```

#### Frontend

```bash
cd client
npm install
cp .env.example .env
npm run dev
```

#### Access

* Frontend: [http://localhost:5173](http://localhost:5173)
* API: [http://localhost:5000](http://localhost:5000)

#### Create Admin

```bash
cd server
npm run seed:superAdmin
```

---

## Tech Stack

### Frontend

* React 19 (TypeScript)
* Vite
* Mantine + Tailwind CSS
* React Router
* Axios
* Framer Motion (animations)
* Deep-diff (change tracking)

### Backend

* Node.js + Express
* MongoDB + Mongoose
* JWT for authentication
* Multer + FFmpeg for video processing
* OpenAI + Deepgram for AI integration
* Nodemailer for emails (supports Mailpit)
* AWS S3 for file storage
* Zod for validation

---

## Email Testing

Run Mailpit locally:

```bash
docker run -d --name mailpit -p 1025:1025 -p 8025:8025 axllent/mailpit
```

Open UI at [http://localhost:8025](http://localhost:8025)

---

## Environment Variables (.env.example)

```env
# Environment
NODE_ENV=development

# Server
PORT=5000
FRONTEND_URL=http://localhost:5173

# MongoDB
MONGO_URI=mongodb://localhost:27017/360-DB

# JWT Auth
ACCESS_TOKEN_SECRET=mySecretKey
ACCESS_TOKEN_EXPIRE_TIME=7200

# Email
EMAIL_HOST=localhost
EMAIL_PORT=1025
EMAIL_USER=
EMAIL_PASS=
EMAIL_FROM=<EMAIL>

# External APIs
OPENAI_API_KEY=your_openai_api_key_here
DEEPGRAM_API_KEY=your_deepgram_api_key_here

# AWS S3
S3_BUCKET=your_s3_bucket_name_here
S3_ACCESS_KEY=your_s3_access_key_here
S3_SECRET_ACCESS_KEY=your_s3_secret_access_key_here
S3_REGION=your_s3_region_here

# Admin
RETRY_NOTIFICATION_EMAIL=<EMAIL>
```
