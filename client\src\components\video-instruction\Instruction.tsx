import { Button, Stack, Text, List, Title } from "@mantine/core";
import { useMemo } from "react";
import { InstructionData } from "./InstructionData";
import type { videoDataType } from "../../types";
import { IconPoint } from "@tabler/icons-react";

const Instruction = (props: {
	videoType: videoDataType;
	onClose?: () => void;
}) => {
	const instructions = useMemo(() => {
		return InstructionData(props.videoType);
	}, [props.videoType]);

	return (
		<>
			<Stack gap="xs">
				<Text style={{ whiteSpace: "pre-line" }}>
					{instructions.description}
				</Text>

				{instructions.sections &&
					instructions.sections.map((section, index) => (
						<>
							{"title" in section && section.title && (
								<Title order={5}>{section.title}</Title>
							)}
							<Stack key={index} gap="xs">
								<List
									spacing="1"
									size="sm"
									withPadding
									styles={{
										itemWrapper: {
											alignItems: "flex-start",
										},
									}}
									icon={<IconPoint size={"1rem"} />}
								>
									{section.points.map((point, pointIndex) => (
										<List.Item
											key={pointIndex}
											styles={{
												item: {
													padding: 4,
												},
											}}
										>
											{point}
										</List.Item>
									))}
								</List>
							</Stack>
						</>
					))}

				{props.onClose && (
					<Button onClick={props.onClose} fullWidth mt="md">
						Got it!
					</Button>
				)}
			</Stack>
		</>
	);
};

export default Instruction;
