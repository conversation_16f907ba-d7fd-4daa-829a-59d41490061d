import { notifications } from "@mantine/notifications";
import { useState } from "react";
import apiClient from "../config/axios";
import { IconCheck, IconX } from "@tabler/icons-react";
import {
	Button,
	Center,
	Paper,
	PasswordInput,
	Stack,
	Title,
} from "@mantine/core";
import { useParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";

const ResetPassword = () => {
	const [password, setPassword] = useState("");
	const [passwordError, setPasswordError] = useState("");
	const [loading, setLoading] = useState(false);
	const token = useParams().token;
	const navigate = useNavigate();

	const validatePassword = (password: string): boolean => {
		if (!password) {
			setPasswordError("Password is required");
			return false;
		}
		if (password.length < 8) {
			setPasswordError("Password must be at least 8 characters long");
			return false;
		}
		const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d).+$/;
		if (!passwordRegex.test(password)) {
			setPasswordError(
				"Password must contain at least one letter and one number"
			);
			return false;
		}
		setPasswordError("");
		return true;
	};

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();

		if (!validatePassword(password)) {
			return;
		}

		if (!password) {
			notifications.show({
				title: "Validation Error",
				message: "Email is required",
				color: "red",
				icon: <IconX />,
			});
			return;
		}
		setLoading(true);
		try {
			const response = await apiClient.post(
				`/api/auth/reset-password/${token}`,
				{ password }
			);
			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			navigate("/login");
		} catch (error: any) {
			notifications.show({
				title: "Error",
				message:
					error.response?.data?.message || "Failed to reset password",
				color: "red",
				icon: <IconX />,
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<Center mih="100vh" bg="gray.0">
			<Paper withBorder shadow="md" radius="md" p="xl" w={460}>
				<Stack gap="sm" align="center">
					<Title order={2}>Reset Password</Title>

					<form onSubmit={handleSubmit}>
						<Stack gap="md" mt="md">
							<PasswordInput
								label="Password"
								placeholder="Enter password here"
								required
								value={password}
								w={400}
								onChange={e => {
									setPassword(e.target.value);
									if (passwordError) {
										validatePassword(e.target.value);
									}
								}}
								error={passwordError}
								disabled={loading}
							/>

							<Button type="submit">
								{loading ? "In process" : "Reset"}
							</Button>
						</Stack>
					</form>
				</Stack>
			</Paper>
		</Center>
	);
};

export default ResetPassword;
