import { compareSync, hashSync, genSaltSync } from "bcryptjs";
import { APP_CONFIG } from "../config/env.js";

export const checkIfPasswordIsValid = (plainPassword, encPassword) => {
	return compareSync(plainPassword, encPassword);
};

export const generateEncPassword = plainPassword => {
	return hashSync(plainPassword, genSaltSync(4, "a"));
};

export const generatePassword = length => {
	let password = "";
	if (!APP_CONFIG.isProduction) {
		return `Holmes@221`;
	}
	const chars = [
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ",
		"abcdefghijklmnopqrstuvwxyz",
		"@$!%*?&",
		"1234567890",
	];
	for (let j = 0; j < chars.length; j++) {
		password += chars[j].charAt(
			Math.floor(Math.random() * chars[j].length)
		);
	}
	if (length > chars.length) {
		length = length - chars.length;
		for (let i = 0; i < length; i++) {
			const index = Math.floor(Math.random() * chars.length);
			password += chars[index].charAt(
				Math.floor(Math.random() * chars[index].length)
			);
		}
	}
	return password
		.split("")
		.sort(function () {
			return 0.5 - Math.random();
		})
		.join("");
};
