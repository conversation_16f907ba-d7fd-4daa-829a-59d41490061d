import fs from "fs";
import path from "path";
import ffmpeg from "fluent-ffmpeg";
import ffmpegStatic from "ffmpeg-static";
import ffprobeStatic from "ffprobe-static";
import { AUDIO_DIR } from "../constants/paths.js";

// Derive string paths to binaries (ffmpeg-static returns a string; ffprobe-static exposes .path)
const ffmpegBinary =
	typeof ffmpegStatic === "string" ? ffmpegStatic : ffmpegStatic?.path;
const ffprobeBinary =
	typeof ffprobeStatic === "string" ? ffprobeStatic : ffprobeStatic?.path;

if (!ffmpegBinary || typeof ffmpegBinary !== "string") {
	throw new Error("ffmpeg binary path not found from ffmpeg-static.");
}
if (!ffprobeBinary || typeof ffprobeBinary !== "string") {
	throw new Error(
		"ffprobe binary path not found from ffprobe-static (use ffprobeStatic.path)."
	);
}

ffmpeg.setFfmpegPath(ffmpegBinary);
ffmpeg.setFfprobePath(ffprobeBinary);

export const extractAudioFromVideo = async (videoPath, videoId) => {
	if (!fs.existsSync(videoPath)) {
		throw new Error(`Video file not found: ${videoPath}`);
	}
	const inputPath = path.resolve(videoPath);

	const hasAudioStream = await new Promise((resolve, reject) => {
		ffmpeg.ffprobe(inputPath, (err, metadata) => {
			if (err) return reject(new Error(`ffprobe failed: ${err.message}`));
			try {
				const streams = Array.isArray(metadata?.streams)
					? metadata.streams
					: [];
				resolve(streams.some(s => s?.codec_type === "audio"));
			} catch (e) {
				reject(
					new Error(
						`Failed to parse ffprobe metadata: ${(e && e.message) || e}`
					)
				);
			}
		});
	});

	if (!hasAudioStream) {
		throw new Error(`No audio stream found in video`);
	}

	fs.mkdirSync(AUDIO_DIR, { recursive: true });
	const audioFilePath = path.join(AUDIO_DIR, videoId.toString() + ".mp3");
	console.log(`Starting audio extraction for: ${inputPath}`);

	await new Promise((resolve, reject) => {
		ffmpeg(inputPath)
			.noVideo()
			.audioCodec("libmp3lame")
			.audioBitrate("128k")
			.audioFrequency(44100)
			.on("end", () => resolve())
			.on("error", err =>
				reject(
					new Error(`Failed to extract audio: ${err?.message || err}`)
				)
			)
			.save(audioFilePath); // do not call .run() after .save()
	});

	if (!fs.existsSync(audioFilePath)) {
		throw new Error("Audio file not created by FFmpeg.");
	}

	return path.resolve(audioFilePath);
};
