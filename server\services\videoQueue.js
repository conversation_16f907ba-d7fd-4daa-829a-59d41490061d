import fs from "fs";
import path from "path";
import VideoUpload from "../models/VideoUpload.js";
import {
	downloadFromS3,
	getPrefixPathForOnboardingVideo,
	listObjects,
} from "./awsSpace.js";
import { extractAudioFromVideo } from "../utils/extractAudioFromVideo.js";
import { AUDIO_DIR, VIDEO_DIR } from "../constants/paths.js";
import User from "../models/User.js";
import { sendEmail } from "./emaiService.js";
import {
	MAX_VIDEO_PROCESS_RETRY_COUNT,
	ONBOARDING_STEP,
	ProfileStatus,
	USER_FIELD_MAP,
	videoTypeWithSpacing,
} from "../constants/index.js";
import {
	maxRetryLimitTemplate,
	specificVideoProcessedTemplate,
	videoProcessingFailedTemplate,
} from "../utils/emailTemplates.js";
import { APP_CONFIG } from "../config/env.js";
import { extractTextFromSpeechFromOpenAI } from "../utils/extractTextFromSpeech.js";
import { extractDataAndUpdateUser } from "./openAiDataExtraction.js";
import Feedback from "../models/Feedback.js";

const videoQueue = [];
let isProcessing = false;

async function processTranscription({ video, audioFilePath = null }) {
	const videoDB = await VideoUpload.findById(video._id);

	try {
		let transcriptionText = null;
		if (videoDB?.transcription && videoDB?.transcription.length > 0) {
			transcriptionText = videoDB.transcription;
		} else if (audioFilePath) {
			transcriptionText =
				await extractTextFromSpeechFromOpenAI(audioFilePath);
		}

		if (!transcriptionText) {
			throw new Error("No transcription text extracted.");
		}

		await VideoUpload.findByIdAndUpdate(video._id, {
			transcription: transcriptionText,
		});

		await extractDataAndUpdateUser(
			transcriptionText,
			video.videoType,
			video.createdBy
		);

		await VideoUpload.findByIdAndUpdate(video._id, {
			transcriptionStatus: "completed",
		});
	} catch (error) {
		console.error("Error processing transcription:", error);
		throw new Error("Error processing transcription.");
	}
}

async function processVideo(video, userId, filepath, videoFilePath) {
	try {
		console.log(`Video processing started for video ${video._id}`);
		await VideoUpload.findByIdAndUpdate(video._id, {
			transcriptionStatus: "processing",
		});

		console.log(`Download the file from aws to ${videoFilePath}`);
		await downloadFromS3(filepath, videoFilePath);

		console.log(`Extract audio from video ${videoFilePath}`);
		const audioFilePath = await extractAudioFromVideo(
			videoFilePath,
			video._id
		);

		console.log(`Add video to transcription queue`);

		await processTranscription({ video, audioFilePath });

		console.log(`Video processing completed for video ${video._id}`);
	} catch (error) {
		console.log(error);
		if (error.message === "No audio stream found in video") {
			throw new Error("No audio stream found in video");
		}
		await VideoUpload.findByIdAndUpdate(video._id, {
			transcriptionStatus: "failed",
			errorMessage: error.message,
		});
		throw new Error("Error processing video.");
	}
}

async function getPathForVideoProcessing(userId, video) {
	try {
		fs.mkdirSync(VIDEO_DIR, { recursive: true });

		const pathToCheck = getPrefixPathForOnboardingVideo(
			userId,
			video._id.toString()
		);
		console.log(`Get the prefix path for video${video._id}`);
		const files = await listObjects(pathToCheck);
		const filepath = files?.[0].Key;
		if (!filepath) {
			throw new Error(
				"Something went wrong, unable to find the file on aws"
			);
		}
		console.log(filepath);
		const extension = path.extname(filepath);

		const videoFilePath = path.join(
			VIDEO_DIR,
			video._id.toString() + extension
		);
		return { filepath, videoFilePath };
	} catch (error) {
		console.log(error);
		throw new Error("Error processing video in getPathForVideoProcessing");
	}
}

async function handleVideoProcessing(video, userId) {
	let videoFilePath = null;
	try {
		const user = await User.findById(userId);
		const pathData = await getPathForVideoProcessing(userId, video);
		const filepath = pathData.filepath;
		videoFilePath = pathData.videoFilePath;
		if (video && video.transcription && video.transcription.length > 0) {
			await processTranscription({ video });
		} else {
			await processVideo(video, userId, filepath, videoFilePath);
		}

		console.log(
			`send email notification to user ${userId} for ${video.videoType}`
		);
		// Send email notification
		let videoType = videoTypeWithSpacing[video.videoType];

		switch (video.videoType) {
			case "EarlyLife":
				if (user.onboardingStep === ONBOARDING_STEP.EARLY_LIFE_VIDEO) {
					user.onboardingStep = ONBOARDING_STEP.EARLY_LIFE_FORM;
					await user.save();
				}
				if (user.profileStatus === ProfileStatus.ChangesRequested) {
					await Feedback.updateMany(
						{
							recipient: userId,
							isActive: true,
							"feedbackMessage.earlyLife": { $exists: true },
						},
						{
							$set: { "actionTaken.earlyLife": true },
						}
					);
				}
				break;
			case "ProfessionalLife":
				if (
					user.onboardingStep ===
					ONBOARDING_STEP.PROFESSIONAL_LIFE_VIDEO
				) {
					user.onboardingStep =
						ONBOARDING_STEP.PROFESSIONAL_LIFE_FORM;
					await user.save();
				}
				if (user.profileStatus === ProfileStatus.ChangesRequested) {
					await Feedback.updateMany(
						{
							recipient: userId,
							isActive: true,
							"feedbackMessage.professionalLife": {
								$exists: true,
							},
						},
						{
							$set: { "actionTaken.professionalLife": true },
						}
					);
				}
				break;
			case "CurrentLife":
				if (
					user.onboardingStep === ONBOARDING_STEP.CURRENT_LIFE_VIDEO
				) {
					user.onboardingStep = ONBOARDING_STEP.CURRENT_LIFE_FORM;
					await user.save();
				}
				if (user.profileStatus === ProfileStatus.ChangesRequested) {
					await Feedback.updateMany(
						{
							recipient: userId,
							isActive: true,
							"feedbackMessage.currentLife": { $exists: true },
						},
						{
							$set: { "actionTaken.currentLife": true },
						}
					);
				}
				break;
			default:
				break;
		}

		await sendEmail({
			to: user.email,
			subject: `Your ${videoType} Video is Processed!`,
			html: specificVideoProcessedTemplate(
				user.firstName,
				user.secondName,
				videoType,
				`${APP_CONFIG.FRONTEND_URL}`
			),
		});
		console.log("Email sent successfully");
	} catch (error) {
		console.log("error in handleVideoProcessing", error);
		if (error.message === "No audio stream found in video") {
			await VideoUpload.findByIdAndUpdate(video._id, {
				transcriptionStatus: "failed",
				errorMessage: error.message,
			});
			try {
				const user = await User.findById(userId);
				if (user && user.displayStatus === true) {
					await sendEmail({
						to: user.email,
						subject: `${videoTypeWithSpacing[video.videoType]} Video Processing Failed`,
						html: videoProcessingFailedTemplate(
							videoTypeWithSpacing[video.videoType],
							`${APP_CONFIG.FRONTEND_URL}`
						),
					});
				}
			} catch (error) {
				console.log(error);
			}
		} else if (video.retryCount < MAX_VIDEO_PROCESS_RETRY_COUNT) {
			video.retryCount += 1;
			await video.save();
			videoQueue.push({ video, userId });
			startProcessing();
		} else {
			const user = await User.findById(userId);

			const latestCompletedVideo = await VideoUpload.findOne({
				createdBy: userId,
				videoType: video.videoType,
				transcriptionStatus: "completed",
			}).sort({ updatedAt: -1 });

			if (latestCompletedVideo) {
				user[
					USER_FIELD_MAP[video.videoType].onboardingLifeData
				].videoId = latestCompletedVideo._id;
				await user.save();
			}
			await sendEmail({
				to: APP_CONFIG.RETRY_NOTIFICATION_EMAIL,
				subject: `${videoTypeWithSpacing[video.videoType]} Video Processing Failed`,
				html: maxRetryLimitTemplate(
					videoTypeWithSpacing[video.videoType],
					user.email
				),
			});
		}
	} finally {
		// Clean up the downloaded video file and audio file
		if (videoFilePath && fs.existsSync(videoFilePath)) {
			fs.unlinkSync(videoFilePath);
		}
		const audioFilePath = path.join(
			AUDIO_DIR,
			video._id.toString() + ".mp3"
		);
		console.log(audioFilePath);
		if (audioFilePath && fs.existsSync(audioFilePath)) {
			fs.unlinkSync(audioFilePath);
		}
	}
}

async function startProcessing() {
	if (isProcessing || videoQueue.length === 0) {
		return;
	}

	isProcessing = true;
	const { video, userId } = videoQueue.shift();

	await handleVideoProcessing(video, userId);

	isProcessing = false;
	startProcessing();
}

export function addVideoToQueue(data) {
	videoQueue.push(data);
	startProcessing();
}

const addVideoToQueueAfterRestartServer = async () => {
	try {
		const processingVideos = await VideoUpload.find({
			transcriptionStatus: "processing",
		});

		const pendingVideos = await VideoUpload.find({
			transcriptionStatus: "pending",
		});

		// Add processing videos to the front of the queue
		for (const video of processingVideos) {
			const user = await User.findById(video.createdBy);
			if (
				user &&
				user.displayStatus === true &&
				video.retryCount < MAX_VIDEO_PROCESS_RETRY_COUNT
			) {
				videoQueue.unshift({
					video,
					userId: video.createdBy,
				});
			}
		}

		// Add pending videos to the rear of the queue
		for (const video of pendingVideos) {
			const user = await User.findById(video.createdBy);
			if (
				user &&
				user.displayStatus === true &&
				video.retryCount < MAX_VIDEO_PROCESS_RETRY_COUNT
			) {
				videoQueue.push({
					video,
					userId: video.createdBy,
				});
			}
		}
		startProcessing();
	} catch (error) {
		console.log(error);
	}
};

addVideoToQueueAfterRestartServer();
