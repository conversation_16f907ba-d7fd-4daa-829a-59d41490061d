import DeepDiff from "deep-diff";

import type {
	CurrentLifeDataType,
	Diff<PERSON>hange,
	EarlyLifeDataType,
	ProfessionalLifeDataType,
} from "../types";

type IndexableData =
	| EarlyLifeDataType
	| ProfessionalLifeDataType
	| CurrentLifeDataType;

type Identifiable = {
	_id?: string | number;
	name?: string;
	role?: string;
	[key: string]: unknown;
};

export const getChangedValue = (
	path: (string | number)[],
	changes: DiffChange[] | null
) => {
	if (!changes) return null;

	const change = changes.find(
		c =>
			c.path?.length === path.length &&
			c.path.every((segment, index) => segment === path[index])
	);
	if (!change) return null;

	switch (change.kind) {
		case "E":
			return {
				oldValue: change.lhs,
				newValue: change.rhs,
				type: "edited" as const,
			};
		case "N":
			return {
				oldValue: null,
				newValue: change.rhs,
				type: "added" as const,
			};
		case "D":
			return {
				oldValue: change.lhs,
				newValue: null,
				type: "deleted" as const,
			};
		default:
			return null;
	}
};

function safeLookup<T extends IndexableData>(
	data: T | undefined,
	key: string
): unknown {
	if (!data) return undefined;

	if (key in data) {
		return data[key as keyof T];
	}

	return undefined;
}

function getGroupKey(
	path: (string | number)[],
	originalData: IndexableData,
	updatedData: IndexableData
): string {
	if (!path || path.length === 0) return "root";

	const root = String(path[0]);

	// If pointing inside an array
	if (typeof path[1] === "number") {
		// Try to detect from originalData first, fallback to updatedData
		const arr =
			safeLookup(originalData, root) ?? safeLookup(updatedData, root);

		// If array of primitives (string, number, boolean)
		if (
			Array.isArray(arr) &&
			arr.length > 0 &&
			typeof arr[0] !== "object"
		) {
			return root; // group all primitive arrays by root key only
		}

		// Otherwise array of objects → group by index
		return `${root}.${path[1]}`;
	}

	// Otherwise, top-level field
	return root;
}

function createContentBasedArrayDiff<T>(
	originalArray: T[] = [],
	updatedArray: T[] = [],
	path: (string | number)[]
): DiffChange[] {
	const changes: DiffChange[] = [];

	if (originalArray.length > 0 && typeof originalArray[0] !== "object") {
		const originalSet = new Set(originalArray);
		const updatedSet = new Set(updatedArray);

		// Find additions
		updatedArray.forEach(item => {
			if (!originalSet.has(item)) {
				const newItemCount = changes.filter(
					change => change.kind === "A" && change.item?.kind === "N"
				).length;
				const newIndex = originalArray.length + newItemCount;
				changes.push({
					kind: "A",
					path,
					index: newIndex,
					item: { kind: "N", rhs: item },
				} as DiffChange);
			}
		});

		// Find deletions
		originalArray.forEach((item, index) => {
			if (!updatedSet.has(item)) {
				changes.push({
					kind: "A",
					path,
					index,
					item: { kind: "D", lhs: item },
				} as DiffChange);
			}
		});
		return changes;
	}

	const originalObjectArray = originalArray as Identifiable[];
	const updatedObjectArray = updatedArray as Identifiable[];

	const getItemKey = (item: Identifiable): string =>
		String(item._id || item.name || item.role || JSON.stringify(item));

	const originalMap = new Map(
		originalObjectArray.map(item => [getItemKey(item), item])
	);
	const updatedMap = new Map(
		updatedObjectArray.map(item => [getItemKey(item), item])
	);

	// Find additions
	updatedObjectArray.forEach(item => {
		const key = getItemKey(item);
		const newItemCount = changes.filter(
			change => change.kind === "A" && change.item?.kind === "N"
		).length;

		const newIndex = originalArray.length + newItemCount;
		if (!originalMap.has(key)) {
			changes.push({
				kind: "A",
				path,
				index: newIndex,
				item: { kind: "N", rhs: item },
			} as DiffChange);
		}
	});

	// Find deletions
	originalObjectArray.forEach((item, index) => {
		const key = getItemKey(item);
		if (!updatedMap.has(key)) {
			changes.push({
				kind: "A",
				path,
				index,
				item: { kind: "D", lhs: item },
			} as DiffChange);
		}
	});

	// Find modifications
	originalObjectArray.forEach((originalItem, index) => {
		const key = getItemKey(originalItem);
		const updatedItem = updatedMap.get(key);

		if (updatedItem) {
			if (
				path[0] === "subsequentJobs" &&
				Array.isArray(originalItem.roles) &&
				Array.isArray(updatedItem.roles)
			) {
				const roleDiffs = createContentBasedArrayDiff(
					originalItem.roles || [],
					updatedItem.roles || [],
					[...path, index, "roles"]
				);
				changes.push(...roleDiffs);
			}

			const itemDiff = DeepDiff.diff(originalItem, updatedItem);
			if (itemDiff) {
				itemDiff
					.filter(diff => !diff.path?.includes("roles"))
					.forEach(diff => {
						changes.push({
							...diff,
							path: [...path, index, ...(diff.path || [])],
						} as DiffChange);
					});
			}
		}
	});
	return changes;
}

export const getDiff = <T extends IndexableData>({
	originalData,
	updatedData,
	ignoredKeys,
}: {
	originalData: T;
	updatedData: T;
	ignoredKeys: (keyof T)[];
}) => {
	if (!originalData || !updatedData) return null;

	const changes: DiffChange[] = [];

	(Object.keys(updatedData) as (keyof T)[]).forEach(key => {
		if (ignoredKeys.includes(key)) return;

		const originalValue = originalData[key];
		const updatedValue = updatedData[key];

		// Handle arrays
		if (Array.isArray(originalValue) || Array.isArray(updatedValue)) {
			const arrayChanges = createContentBasedArrayDiff(
				(originalValue as unknown[] | undefined) || [],
				(updatedValue as unknown[] | undefined) || [],
				[key as string]
			);
			changes.push(...arrayChanges);
		} else {
			if (key === "firstJob") {
				const companyNameDiff = DeepDiff.diff(
					{
						companyName: (originalValue as { companyName?: string })
							?.companyName,
					},
					{
						companyName: (updatedValue as { companyName?: string })
							?.companyName,
					}
				);
				if (companyNameDiff) {
					companyNameDiff.forEach(diff => {
						changes.push({
							...diff,
							path: [key as string, ...(diff.path || [])],
						} as DiffChange);
					});
				}

				const arrayChanges = createContentBasedArrayDiff(
					(originalValue as { roles?: unknown[] })?.roles || [],
					(updatedValue as { roles?: unknown[] })?.roles || [],
					[key as string, "roles"]
				);
				changes.push(...arrayChanges);
			} else {
				const regularDiff = DeepDiff.diff(
					{ [key]: originalValue },
					{ [key]: updatedValue }
				);
				if (regularDiff) {
					changes.push(...regularDiff);
				}
			}
		}
	});

	if (changes.length > 0) {
		const grouped = new Map<string, DiffChange[]>();
		changes.forEach(change => {
			if (!change.path) return;
			const groupKey = getGroupKey(
				change.path,
				originalData,
				updatedData
			);
			if (!grouped.has(groupKey)) grouped.set(groupKey, []);
			grouped.get(groupKey)!.push(change);
		});

		return {
			total: grouped.size,
			changes,
		};
	}

	return null;
};

export const getDiffStyle = (isDeleted = false, isNew = false) => {
	if (isDeleted) {
		return {
			backgroundColor: "var(--mantine-color-red-1)",
			padding: "10px",
			borderRadius: "8px",
			borderLeft: "4px solid var(--mantine-color-red-3)",
		};
	}
	if (isNew) {
		return {
			backgroundColor: "var(--mantine-color-green-1)",
			padding: "10px",
			borderRadius: "8px",
			borderLeft: "4px solid var(--mantine-color-green-3)",
		};
	}
	return {};
};
