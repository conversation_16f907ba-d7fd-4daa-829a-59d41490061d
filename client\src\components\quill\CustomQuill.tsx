import { useEffect, useRef, useState } from "react";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";

const defaultCustomModules = {
	toolbar: [
		"bold",
		"italic",
		"underline",
		"strike",
		{ list: "ordered" },
		{ list: "bullet" },
		"link",
		{ color: [] },
		"clean",
	],
};

interface QuillProps {
	value?: string;
	onChange?: (_value: string) => void;
	minHeight?: string;
	modules?: typeof ReactQuill.defaultProps.modules;
	readOnly?: boolean;
	customStyle?: string;
}

export default function CustomQuill(props: QuillProps) {
	const isFirstRender = useRef(true);
	const { value, onChange } = props;
	const quillRef = useRef<ReactQuill | null>(null);
	const [quillValue, setValue] = useState("");

	useEffect(() => {
		setValue(value ?? "");
	}, [value]);

	const handleChange = (val: string) => {
		if (isFirstRender.current) {
			isFirstRender.current = false;
			return;
		}
		setValue(val);
		if (onChange) {
			onChange(val);
		}
	};

	return (
		<>
			<style>
				{`
				.invite-edit-form-container .ql-editor {
					min-height: ${props.minHeight || "200px"};
					font-size: 14px;
				}
				${props.customStyle}
			`}
			</style>
			<ReactQuill
				ref={quillRef}
				theme="snow"
				value={quillValue}
				onChange={handleChange}
				className="invite-edit-form-container"
				bounds=".invite-edit-form-container"
				modules={props.modules ?? defaultCustomModules}
				placeholder="Write something here..."
				preserveWhitespace
				readOnly={props.readOnly}
			/>
		</>
	);
}
