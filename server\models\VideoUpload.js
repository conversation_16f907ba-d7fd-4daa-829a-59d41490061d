import mongoose from "mongoose";

const videoUploadSchema = new mongoose.Schema(
	{
		transcriptionStatus: {
			type: String,
			enum: ["pending", "processing", "completed", "failed"],
			default: "pending",
		},
		errorMessage: {
			type: String,
		},
		videoType: {
			type: String,
			enum: ["EarlyLife", "ProfessionalLife", "CurrentLife"],
			required: true,
		},
		createdBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},
		transcription: {
			type: String,
		},
		retryCount: {
			type: Number,
			default: 0,
		},
	},
	{
		timestamps: true, // Adds createdAt and updatedAt fields
	}
);

const VideoUpload = mongoose.model("VideoUpload", videoUploadSchema);

export default VideoUpload;
